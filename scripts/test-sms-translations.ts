#!/usr/bin/env tsx

/**
 * Test SMS Translation Variables Script
 * 
 * This script tests that SMS translation variables are properly replaced
 * to help debug the count variable issue.
 */

// Mock i18next for testing
const mockTranslations = {
  'sms:sending.paymentReminder.warningPlural': 'This will send Sms messages to {count} recipients and will consume {count} Sms credits from your balance.',
  'sms:sending.teamMessage.warningPlural': 'This will send Sms messages to {count} recipients and will consume {count} Sms credits from your balance.',
  'sms:sending.teamMessage.warningSmsCountSingle': 'Total: {total} Sms credit will be consumed from your balance.',
  'sms:sending.teamMessage.warningSmsCountPlural': 'Each recipient will receive {smsPerRecipient} Sms messages. Total: {total} Sms credits will be consumed from your balance.',
};

function mockT(key: string): string {
  return mockTranslations[key as keyof typeof mockTranslations] || key;
}

function testTranslationReplacement() {
  console.log('🧪 Testing SMS Translation Variable Replacement');
  console.log('==============================================\n');

  // Test 1: Payment Reminder Dialog
  console.log('📋 Test 1: Payment Reminder Dialog');
  const paymentCount = 2;
  const paymentReminderMessage = mockT('sms:sending.paymentReminder.warningPlural')
    .replace(/{count}/g, paymentCount.toString());
  
  console.log(`Input: ${mockT('sms:sending.paymentReminder.warningPlural')}`);
  console.log(`Output: ${paymentReminderMessage}`);
  console.log(`✅ Contains {count}: ${mockT('sms:sending.paymentReminder.warningPlural').includes('{count}')}`);
  console.log(`✅ Replaced properly: ${!paymentReminderMessage.includes('{count}')}\n`);

  // Test 2: Team Message Dialog - Single SMS
  console.log('📋 Test 2: Team Message Dialog - Single SMS');
  const recipientCount = 2;
  const totalSmsCount = 2; // 2 recipients * 1 SMS each
  const singleSmsMessage = mockT('sms:sending.teamMessage.warningSmsCountSingle')
    .replace('{total}', totalSmsCount.toString());
  
  console.log(`Input: ${mockT('sms:sending.teamMessage.warningSmsCountSingle')}`);
  console.log(`Output: ${singleSmsMessage}`);
  console.log(`✅ Contains {total}: ${mockT('sms:sending.teamMessage.warningSmsCountSingle').includes('{total}')}`);
  console.log(`✅ Replaced properly: ${!singleSmsMessage.includes('{total}')}\n`);

  // Test 3: Team Message Dialog - Multiple SMS
  console.log('📋 Test 3: Team Message Dialog - Multiple SMS');
  const smsPerRecipient = 2;
  const totalMultipleSmsCount = recipientCount * smsPerRecipient; // 2 recipients * 2 SMS each = 4
  const multipleSmsMessage = mockT('sms:sending.teamMessage.warningSmsCountPlural')
    .replace('{smsPerRecipient}', smsPerRecipient.toString())
    .replace('{total}', totalMultipleSmsCount.toString());
  
  console.log(`Input: ${mockT('sms:sending.teamMessage.warningSmsCountPlural')}`);
  console.log(`Output: ${multipleSmsMessage}`);
  console.log(`✅ Contains {smsPerRecipient}: ${mockT('sms:sending.teamMessage.warningSmsCountPlural').includes('{smsPerRecipient}')}`);
  console.log(`✅ Contains {total}: ${mockT('sms:sending.teamMessage.warningSmsCountPlural').includes('{total}')}`);
  console.log(`✅ Replaced properly: ${!multipleSmsMessage.includes('{smsPerRecipient}') && !multipleSmsMessage.includes('{total}')}\n`);

  // Test 4: Edge Cases
  console.log('📋 Test 4: Edge Cases');
  
  // Test with 0 recipients
  const zeroRecipientsMessage = mockT('sms:sending.paymentReminder.warningPlural')
    .replace(/{count}/g, '0');
  console.log(`Zero recipients: ${zeroRecipientsMessage}`);
  
  // Test with undefined/null values (should be handled by safe values)
  const safeRecipientCount = Math.max(0, 0 || 0);
  const safeSmsCount = Math.max(1, 0 || 1);
  const safeTotalSmsCount = safeRecipientCount * safeSmsCount;
  
  console.log(`Safe recipient count: ${safeRecipientCount}`);
  console.log(`Safe SMS count: ${safeSmsCount}`);
  console.log(`Safe total SMS count: ${safeTotalSmsCount}`);
  
  const safeMessage = mockT('sms:sending.teamMessage.warningSmsCountSingle')
    .replace('{total}', safeTotalSmsCount.toString());
  console.log(`Safe message: ${safeMessage}\n`);

  // Test 5: Simulate the exact scenario mentioned by user
  console.log('📋 Test 5: User Scenario - 2 recipients');
  const userScenarioCount = 2;
  const userScenarioMessage = mockT('sms:sending.paymentReminder.warningPlural')
    .replace(/{count}/g, userScenarioCount.toString());
  
  console.log(`User scenario input: ${mockT('sms:sending.paymentReminder.warningPlural')}`);
  console.log(`User scenario output: ${userScenarioMessage}`);
  console.log(`✅ Should show: "This will send Sms messages to 2 recipients and will consume 2 Sms credits from your balance."`);
  console.log(`✅ Actually shows: "${userScenarioMessage}"`);
  console.log(`✅ Match: ${userScenarioMessage === 'This will send Sms messages to 2 recipients and will consume 2 Sms credits from your balance.'}\n`);

  console.log('✅ All translation tests completed!');
  console.log('\n💡 If you\'re still seeing {count} not replaced in the UI:');
  console.log('   1. Check browser console for any JavaScript errors');
  console.log('   2. Verify the translation files are loaded correctly');
  console.log('   3. Check if there are any race conditions in component rendering');
  console.log('   4. Ensure the component state is updating properly');
}

// Run the test
testTranslationReplacement();
