#!/usr/bin/env tsx

/**
 * Check Payment Status Script
 * 
 * This script checks the current status of all payments to help debug
 * payment status issues.
 */

import { db } from '../src/db';
import { payments } from '../src/db/schema';
import { eq, and, gte, lte, desc } from 'drizzle-orm';
import { getTodayAsLocalString } from '../lib/utils/date-formatter';

async function main() {
  try {
    console.log('📊 Payment Status Report');
    console.log('========================');
    
    const todayString = getTodayAsLocalString();
    console.log(`📅 Today: ${todayString}\n`);
    
    // Get all payments grouped by status
    const allPayments = await db
      .select({
        id: payments.id,
        tenantId: payments.tenantId,
        athleteId: payments.athleteId,
        amount: payments.amount,
        date: payments.date,
        dueDate: payments.dueDate,
        status: payments.status,
        description: payments.description,
        createdAt: payments.createdAt,
        updatedAt: payments.updatedAt
      })
      .from(payments)
      .orderBy(desc(payments.dueDate));
    
    // Group by status
    const paymentsByStatus = allPayments.reduce((acc, payment) => {
      if (!acc[payment.status]) {
        acc[payment.status] = [];
      }
      acc[payment.status].push(payment);
      return acc;
    }, {} as Record<string, typeof allPayments>);
    
    console.log('📈 Payment Summary by Status:');
    Object.entries(paymentsByStatus).forEach(([status, statusPayments]) => {
      console.log(`   ${status.toUpperCase()}: ${statusPayments.length} payments`);
    });
    
    console.log('\n🔍 Detailed Analysis:\n');
    
    // Check pending payments
    const pendingPayments = paymentsByStatus['pending'] || [];
    console.log(`📋 PENDING PAYMENTS (${pendingPayments.length}):`);
    
    if (pendingPayments.length === 0) {
      console.log('   No pending payments found.');
    } else {
      const futurePending = pendingPayments.filter(p => p.dueDate > todayString);
      const todayPending = pendingPayments.filter(p => p.dueDate === todayString);
      const pastPending = pendingPayments.filter(p => p.dueDate < todayString);
      
      console.log(`   • Future due dates: ${futurePending.length}`);
      console.log(`   • Due today: ${todayPending.length}`);
      console.log(`   • Past due dates (should be overdue): ${pastPending.length}`);
      
      if (pastPending.length > 0) {
        console.log('\n   ⚠️  PENDING PAYMENTS THAT SHOULD BE OVERDUE:');
        pastPending.forEach(payment => {
          console.log(`      - ID: ${payment.id.substring(0, 8)}... | Due: ${payment.dueDate} | Amount: ${payment.amount} | Created: ${payment.createdAt?.toISOString()}`);
        });
      }
      
      if (futurePending.length > 0) {
        console.log('\n   ✅ FUTURE PENDING PAYMENTS (correctly pending):');
        futurePending.slice(0, 5).forEach(payment => {
          console.log(`      - ID: ${payment.id.substring(0, 8)}... | Due: ${payment.dueDate} | Amount: ${payment.amount}`);
        });
        if (futurePending.length > 5) {
          console.log(`      ... and ${futurePending.length - 5} more`);
        }
      }
    }
    
    // Check overdue payments
    const overduePayments = paymentsByStatus['overdue'] || [];
    console.log(`\n📋 OVERDUE PAYMENTS (${overduePayments.length}):`);
    
    if (overduePayments.length === 0) {
      console.log('   No overdue payments found.');
    } else {
      console.log('   Recent overdue payments:');
      overduePayments.slice(0, 10).forEach(payment => {
        console.log(`      - ID: ${payment.id.substring(0, 8)}... | Due: ${payment.dueDate} | Amount: ${payment.amount} | Updated: ${payment.updatedAt?.toISOString()}`);
      });
      if (overduePayments.length > 10) {
        console.log(`      ... and ${overduePayments.length - 10} more`);
      }
    }
    
    // Check paid payments
    const paidPayments = paymentsByStatus['paid'] || [];
    console.log(`\n📋 PAID PAYMENTS (${paidPayments.length}):`);
    console.log(`   Total paid payments: ${paidPayments.length}`);
    
    // Check for specific date mentioned by user (2025-07-16)
    const specificDate = '2025-07-16';
    const paymentsOnSpecificDate = allPayments.filter(p => p.dueDate === specificDate);
    
    console.log(`\n🔍 PAYMENTS DUE ON ${specificDate}:`);
    if (paymentsOnSpecificDate.length === 0) {
      console.log('   No payments found with this due date.');
    } else {
      paymentsOnSpecificDate.forEach(payment => {
        console.log(`   - ID: ${payment.id.substring(0, 8)}... | Status: ${payment.status} | Amount: ${payment.amount} | Updated: ${payment.updatedAt?.toISOString()}`);
      });
    }
    
    console.log('\n✅ Payment status check completed!');
    
  } catch (error) {
    console.error('❌ Error checking payment status:', error);
    process.exit(1);
  }
}

// Run the script
main().catch(console.error);
