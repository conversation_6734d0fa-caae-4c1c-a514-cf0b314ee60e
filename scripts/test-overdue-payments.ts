#!/usr/bin/env tsx

/**
 * Test Overdue Payments Update Script
 * 
 * This script allows manual testing of the overdue payment update logic
 * to debug why payments are not being marked as overdue.
 * 
 * Usage:
 *   tsx scripts/test-overdue-payments.ts
 *   npm run test-overdue-payments
 */

import { runOverdueUpdateForTenant } from '../lib/payment-scheduler';
import { db } from '../src/db';
import { payments } from '../src/db/schema';
import { eq, and, lte } from 'drizzle-orm';

async function main() {
  try {
    console.log('🔍 Testing Overdue Payments Update');
    console.log('=====================================');
    
    const today = new Date();
    const todayString = today.toISOString().split('T')[0];
    console.log(`📅 Today: ${todayString}`);
    
    // First, let's check what pending payments exist with due dates <= today
    console.log('\n📋 Checking pending payments with due dates <= today...');
    
    const pendingPayments = await db
      .select({
        id: payments.id,
        tenantId: payments.tenantId,
        athleteId: payments.athleteId,
        amount: payments.amount,
        dueDate: payments.dueDate,
        status: payments.status,
        description: payments.description
      })
      .from(payments)
      .where(
        and(
          eq(payments.status, "pending"),
          lte(payments.dueDate, todayString)
        )
      );
    
    console.log(`Found ${pendingPayments.length} pending payments with due dates <= ${todayString}:`);
    
    if (pendingPayments.length === 0) {
      console.log('ℹ️  No pending payments found that should be overdue.');
      console.log('   This could mean:');
      console.log('   1. All payments are already marked as overdue');
      console.log('   2. No payments have due dates <= today');
      console.log('   3. All payments are in other statuses (paid, cancelled, etc.)');
    } else {
      // Group by tenant
      const paymentsByTenant = pendingPayments.reduce((acc, payment) => {
        if (!acc[payment.tenantId]) {
          acc[payment.tenantId] = [];
        }
        acc[payment.tenantId].push(payment);
        return acc;
      }, {} as Record<string, typeof pendingPayments>);
      
      console.log('\n📊 Payments by tenant:');
      Object.entries(paymentsByTenant).forEach(([tenantId, tenantPayments]) => {
        console.log(`\n🏢 Tenant: ${tenantId}`);
        tenantPayments.forEach(payment => {
          console.log(`   - ID: ${payment.id.substring(0, 8)}... | Due: ${payment.dueDate} | Amount: ${payment.amount} | ${payment.description}`);
        });
      });
      
      // Now run the overdue update for each tenant
      console.log('\n🔄 Running overdue payment updates...');
      
      for (const tenantId of Object.keys(paymentsByTenant)) {
        console.log(`\n🏢 Processing tenant: ${tenantId}`);
        try {
          const result = await runOverdueUpdateForTenant(tenantId);
          console.log(`✅ Updated ${result.length} payments to overdue status for tenant ${tenantId}`);
          
          if (result.length > 0) {
            result.forEach(payment => {
              console.log(`   - Updated: ${payment.id.substring(0, 8)}... | Due: ${payment.dueDate} | Amount: ${payment.amount}`);
            });
          }
        } catch (error) {
          console.error(`❌ Error processing tenant ${tenantId}:`, error);
        }
      }
    }
    
    // Final check - show all overdue payments
    console.log('\n📋 Final check - all overdue payments:');
    const overduePayments = await db
      .select({
        id: payments.id,
        tenantId: payments.tenantId,
        dueDate: payments.dueDate,
        amount: payments.amount,
        status: payments.status,
        updatedAt: payments.updatedAt
      })
      .from(payments)
      .where(eq(payments.status, "overdue"));
    
    console.log(`Found ${overduePayments.length} overdue payments total:`);
    overduePayments.forEach(payment => {
      console.log(`   - ID: ${payment.id.substring(0, 8)}... | Due: ${payment.dueDate} | Amount: ${payment.amount} | Updated: ${payment.updatedAt?.toISOString()}`);
    });
    
    console.log('\n✅ Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the script
main().catch(console.error);
