/**
 * Utility functions for consistent date formatting across the application
 * Handles timezone issues by using local timezone instead of UTC
 */

/**
 * Formats a date to YYYY-MM-DD string using local timezone
 * Prevents timezone-related date shifting issues
 */
export function formatDateToLocalString(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * Gets today's date as YYYY-MM-DD string in local timezone
 */
export function getTodayAsLocalString(): string {
  return formatDateToLocalString(new Date());
}

/**
 * Parses various date formats and returns a formatted YYYY-MM-DD string
 * Handles Excel date formats including DD/MM/YYYY and other common formats
 */
export function parseAndFormatDate(dateValue: any): string {
  if (!dateValue) return '';

  // If it's already a Date object
  if (dateValue instanceof Date) {
    return formatDateToLocalString(dateValue);
  }

  // If it's a string, try to parse different formats
  if (typeof dateValue === 'string') {
    const trimmedValue = dateValue.trim();
    if (!trimmedValue) return '';

    let date: Date | null = null;

    // Try DD/MM/YYYY format first (common in Excel)
    if (trimmedValue.match(/^\d{1,2}\/\d{1,2}\/\d{4}$/)) {
      const [day, month, year] = trimmedValue.split('/');
      date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
    } 
    // Try DD-MM-YYYY format
    else if (trimmedValue.match(/^\d{1,2}-\d{1,2}-\d{4}$/)) {
      const [day, month, year] = trimmedValue.split('-');
      date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
    }
    // Try DD.MM.YYYY format
    else if (trimmedValue.match(/^\d{1,2}\.\d{1,2}\.\d{4}$/)) {
      const [day, month, year] = trimmedValue.split('.');
      date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
    }
    // Try YYYY-MM-DD format (ISO format)
    else if (trimmedValue.match(/^\d{4}-\d{1,2}-\d{1,2}$/)) {
      const [year, month, day] = trimmedValue.split('-');
      date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
    }
    // Try other formats using Date constructor
    else {
      date = new Date(trimmedValue);
    }

    // Validate the parsed date
    if (date && !isNaN(date.getTime())) {
      return formatDateToLocalString(date);
    }
  }

  // If it's a number (Excel serial date)
  if (typeof dateValue === 'number') {
    // Excel serial date starts from 1900-01-01
    const excelEpoch = new Date(1900, 0, 1);
    const date = new Date(excelEpoch.getTime() + (dateValue - 1) * 24 * 60 * 60 * 1000);
    if (!isNaN(date.getTime())) {
      return formatDateToLocalString(date);
    }
  }

  return '';
}

/**
 * Parses a date value and returns a formatted string, with fallback to today's date
 * Useful for registration dates where we want to default to today if no date provided
 */
export function parseAndFormatDateWithTodayFallback(dateValue: any): string {
  const formatted = parseAndFormatDate(dateValue);
  return formatted || getTodayAsLocalString();
}
