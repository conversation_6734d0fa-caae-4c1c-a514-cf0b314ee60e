"use server";

import { revalidatePath } from 'next/cache';
import { facilityService } from '../services/facility.service';

// Facilities
export async function getFacilities() {
  try {
    // Try using the service first
    const service = facilityService();
    const result = await service.getFacilities();

    if (result.success) {
      return result.data || [];
    }

    // If service fails, log error and throw
    console.error("Service call failed:", result.error);
    throw new Error(result.error?.userMessage || "Failed to get facilities");
  } catch (error) {
    console.error("Error getting facilities:", error);
    throw error;
  }
}

export async function getFacilityById(id: string) {
  try {
    // Try using the service first
    const service = facilityService();
    const result = await service.getFacilityById(id);

    if (result.success) {
      return result.data;
    }

    // If service fails, log error and throw
    console.error("Service call failed:", result.error);
    throw new Error(result.error?.userMessage || `Failed to get facility with ID ${id}`);
  } catch (error) {
    console.error("Error getting facility by ID:", error);
    throw error;
  }
}

export async function getFacilitySchedules(facilityId: string) {
  const service = facilityService();
  const result = await service.getFacilitySchedules(facilityId);

  if (!result.success) {
    throw new Error(result.error?.message || 'Failed to get facility schedules');
  }

  return result.data;
}

export async function getAllFacilitySchedules() {
  const service = facilityService();
  const result = await service.getAllFacilitySchedules();

  if (!result.success) {
    throw new Error(result.error?.message || 'Failed to get all facility schedules');
  }

  return result.data || [];
}

export async function createFacility(data: {
  name: string;
  type: 'field' | 'court' | 'pool' | 'studio' | 'other';
  address: string;
  totalCapacity?: number;
  length?: string;
  width?: string;
  dimensionUnit?: 'meters' | 'feet';
}) {
  console.log('Creating facility with data:', data);
  try {
    // Try using the service first
    const service = facilityService();
    const result = await service.createFacility(data);

    if (result.success) {
      console.log('Facility created successfully via service:', result.data);
      // Revalidate paths
      revalidatePath('/facilities');
      return { success: true, data: result.data };
    }else{
      console.error("Service call failed:", result.error);
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }
      return { success: false, error: result.error?.userMessage || 'Failed to create facility' , errorType: 'general' };
    }
  } catch (error) {
    console.error('Error creating facility:', error);
    return { success: false, error: error instanceof Error ? error.message : "Failed to create facility", errorType: 'general' };
  }
}

export async function updateFacility(id: string, data: {
  name?: string;
  type?: 'field' | 'court' | 'pool' | 'studio' | 'other';
  address?: string;
  totalCapacity?: number;
  length?: string;
  width?: string;
  dimensionUnit?: 'meters' | 'feet';
}) {
  try {
    // Try using the service first
    const service = facilityService();
    const result = await service.updateFacility(id, data);

    if (result.success) {
      // Revalidate paths
      revalidatePath('/facilities');
      revalidatePath(`/facilities/${id}`);
      return { success: true, data: result.data };
    }else{
      console.error(`Failed to update facility with ID ${id}. Service call failed:`, result.error);
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }
      return { success: false, error: result.error?.userMessage || 'Failed to update facility' , errorType: 'general' };
    }
  } catch (error) {
    console.error(`Error updating facility with ID ${id}. Error : `, error);
    return { success: false, error: error instanceof Error ? error.message : "Failed to update facility", errorType: 'general' };
  }
}

export async function deleteFacility(id: string) {
  try {
    // Try using the service first
    const service = facilityService();
    const result = await service.deleteFacility(id);

    if (result.success) {
      // Revalidate paths
      revalidatePath('/facilities');
      return true;
    }

    // If service fails, log error and throw
    console.error("Service call failed:", result.error);
    throw new Error(result.error?.userMessage || `Failed to delete facility with ID ${id}`);
  } catch (error) {
    console.error("Error deleting facility:", error);
    throw error;
  }
}