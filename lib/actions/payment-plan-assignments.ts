"use server";

import { getServerTenantId, getServerUserId } from '../tenant-utils-server';
import { paymentPlanAssignmentService } from '../services';
import { AssignPaymentPlanParams } from '../payment-plan-utils';

// Payment Plan Assignment Actions

export async function assignPaymentPlan(params: Omit<AssignPaymentPlanParams, 'tenantId' | 'userId'>) {
  const tenantId = await getServerTenantId();
  const userId = await getServerUserId();
  
  const result = await paymentPlanAssignmentService().assignPaymentPlan({
    athleteId: params.athleteId,
    planId: params.planId,
    teamId: params.teamId,
    isActive: params.isActive
  }, userId?.toString() || undefined, tenantId || undefined);
  
  if (!result.success) {
    throw new Error(result.error?.message || 'Failed to assign payment plan');
  }
  
  return result.data;
}

export async function getAthleteAssignedPlans(athleteId: string) {
  const tenantId = await getServerTenantId();
  const userId = await getServerUserId();
  
  const result = await paymentPlanAssignmentService().getAthleteAssignedPlans(
    athleteId, 
    userId?.toString() || undefined, 
    tenantId || undefined
  );
  
  if (!result.success) {
    throw new Error(result.error?.message || 'Failed to get athlete assigned plans');
  }
  
  return result.data || [];
}

export async function getPaymentPlanStats() {
  const tenantId = await getServerTenantId();
  const userId = await getServerUserId();
  
  const result = await paymentPlanAssignmentService().getPaymentPlanStats(
    userId?.toString() || undefined, 
    tenantId || undefined
  );
  
  if (!result.success) {
    throw new Error(result.error?.message || 'Failed to get payment plan stats');
  }
  
  return result.data;
}

export async function deactivateAssignment(assignmentId: string) {
  const tenantId = await getServerTenantId();
  const userId = await getServerUserId();
  
  const result = await paymentPlanAssignmentService().deactivateAssignment(
    assignmentId, 
    userId?.toString() || undefined, 
    tenantId || undefined
  );
  
  if (!result.success) {
    throw new Error(result.error?.message || 'Failed to deactivate assignment');
  }
  
  return result.data;
}

export async function reactivateAssignment(assignmentId: string) {
  const tenantId = await getServerTenantId();
  const userId = await getServerUserId();
  
  const result = await paymentPlanAssignmentService().reactivateAssignment(
    assignmentId, 
    userId?.toString() || undefined, 
    tenantId || undefined
  );
  
  if (!result.success) {
    throw new Error(result.error?.message || 'Failed to reactivate assignment');
  }
  
  return result.data;
}

export async function deleteAssignment(assignmentId: string, preservePaymentStatus: boolean = false) {
  const tenantId = await getServerTenantId();
  const userId = await getServerUserId();
  
  const result = await paymentPlanAssignmentService().deleteAssignment(
    assignmentId, 
    userId?.toString() || undefined, 
    tenantId || undefined,
    preservePaymentStatus
  );
  
  if (!result.success) {
    throw new Error(result.error?.message || 'Failed to delete assignment');
  }
  
  return result.data;
}

export async function runScheduledPaymentCreation() {
  const tenantId = await getServerTenantId();
  const userId = await getServerUserId();
  
  const result = await paymentPlanAssignmentService().runScheduledPaymentCreation(
    userId?.toString() || undefined, 
    tenantId || undefined
  );
  
  if (!result.success) {
    throw new Error(result.error?.message || 'Failed to run scheduled payment creation');
  }
  
  return result.data;
}

export async function runOverduePaymentCheck() {
  const tenantId = await getServerTenantId();
  const userId = await getServerUserId();
  
  const result = await paymentPlanAssignmentService().runOverduePaymentCheck(
    userId?.toString() || undefined, 
    tenantId || undefined
  );
  
  if (!result.success) {
    throw new Error(result.error?.message || 'Failed to run overdue payment check');
  }
  
  return result.data;
}
