"use server";

import { getServerTenantId } from '../tenant-utils-server';
import { teamService } from '../services';

// Teams
export async function getTeams() {
  try {
    const tenantId = await getServerTenantId();
    const result = await teamService().getTeams(undefined, tenantId || undefined);
    
    if (!result.success) {
      console.error("getTeams error:", result.error);
      throw new Error(result.error?.userMessage || "Failed to get teams");
    }
    
    return result.data || [];
  } catch (error) {
    console.error("getTeams error:", error);
    throw error;
  }
}

export async function getTeamsPaginated(
  page: number = 1,
  limit: number = 10,
  search?: string,
  sortBy?: string,
  sortOrder?: 'asc' | 'desc',
  filters?: {
    name?: string;
    description?: string;
    schoolName?: string;
    branchName?: string;
    instructorName?: string;
  }
) {
  const tenantId = await getServerTenantId();
  const result = await teamService().getTeamsPaginated(
    page,
    limit,
    search,
    sortBy,
    sortOrder,
    filters,
    undefined,
    tenantId || undefined
  );
  
  if (!result.success) {
    throw new Error(result.error?.message || 'Failed to get paginated teams');
  }
  
  return result.data;
}

export async function getTeamById(id: string) {
  try {
    const tenantId = await getServerTenantId();
    const result = await teamService().getTeamById(id, undefined, tenantId || undefined);
    
    if (!result.success) {
      console.error("getTeamById error:", result.error);
      throw new Error(result.error?.userMessage || `Failed to get team with ID ${id}`);
    }
    
    return result.data;
  } catch (error) {
    console.error("getTeamById error:", error);
    throw error;
  }
}

export async function createTeamWithSchedules(teamData: any, schedules: any[]) {
  try {
    const tenantId = await getServerTenantId();
    const result = await teamService().createTeamWithSchedules(
      teamData, 
      schedules, 
      undefined, 
      tenantId || undefined
    );
    
    if (!result.success) {
      console.error("createTeamWithSchedules error:", result.error);
      throw new Error(result.error?.userMessage || "Failed to create team with schedules");
    }
    
    return result.data;
  } catch (error) {
    console.error("createTeamWithSchedules error:", error);
    throw error;
  }
}

export async function updateTeamWithSchedules(teamId: string, teamData: any, schedules: any[]) {
  try {
    const tenantId = await getServerTenantId();
    const result = await teamService().updateTeamWithSchedules(
      teamId, 
      teamData, 
      schedules, 
      undefined, 
      tenantId || undefined
    );
    
    if (!result.success) {
      console.error("updateTeamWithSchedules error:", result.error);
      throw new Error(result.error?.userMessage || `Failed to update team with schedules`);
    }
    
    return result.data;
  } catch (error) {
    console.error("updateTeamWithSchedules error:", error);
    throw error;
  }
}

export async function deleteTeam(id: string) {
  try {
    const tenantId = await getServerTenantId();
    const result = await teamService().deleteTeam(id, undefined, tenantId || undefined);
    
    if (!result.success) {
      console.error("deleteTeam error:", result.error);
      throw new Error(result.error?.userMessage || `Failed to delete team with ID ${id}`);
    }
    
    return result.data;
  } catch (error) {
    console.error("deleteTeam error:", error);
    throw error;
  }
}

// Payment Plan Bulk Assignment Actions

export async function checkPaymentPlanConflicts(teamId: string, planId: string, athleteIds: string[]) {
  if (!teamId || !planId || !athleteIds || !Array.isArray(athleteIds)) {
    throw new Error("Missing required fields");
  }

  try {
    const tenantId = await getServerTenantId();
    
    if (!tenantId) {
      throw new Error("Tenant context not found");
    }

    const result = await teamService().checkPaymentPlanConflicts(
      teamId, 
      planId, 
      athleteIds, 
      undefined, 
      tenantId
    );
    
    if (!result.success) {
      console.error("checkPaymentPlanConflicts error:", result.error);
      throw new Error(result.error?.userMessage || "Failed to check payment plan conflicts");
    }
    
    return result.data;
  } catch (error) {
    console.error('Error in checkPaymentPlanConflicts:', error);
    throw error;
  }
}

export async function bulkAssignPaymentPlan(teamId: string, planId: string, athleteIds: string[], force: boolean = false) {
  if (!teamId || !planId || !athleteIds || !Array.isArray(athleteIds)) {
    throw new Error("Missing required fields");
  }

  try {
    const tenantId = await getServerTenantId();
    
    if (!tenantId) {
      throw new Error("Tenant context not found");
    }

    const result = await teamService().bulkAssignPaymentPlan(
      { teamId, planId, athleteIds, force },
      undefined,
      tenantId
    );
    
    if (!result.success) {
      console.error("bulkAssignPaymentPlan error:", result.error);
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }
      return { success: false, error: result.error?.userMessage || "Failed to assign payment plans", errorType: 'general' };
    }else{
      return { success: true, data: result.data };
    }
  } catch (error) {
    console.error('Error in bulkAssignPaymentPlan:', error);
    return { success: false, error: error instanceof Error ? error.message : "Failed to assign payment plans", errorType: 'general' };
  }
}