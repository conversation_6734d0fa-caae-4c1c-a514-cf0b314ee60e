"use server";

import { revalidatePath } from "next/cache";
import { assignPaymentPlan, deactivateAssignment } from "./payment-plan-assignments";
import { addAthleteToTeam } from "./athlete-teams";

export interface PaymentPlanAssignmentData {
  id: string;
  planId: string;
  teamId?: string | null;
  assignedDate: string;
  isActive: boolean;
  lastPaymentDate?: string | null;
  planName: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
  teamName?: string | null;
  athleteName: string;
  athleteSurname: string;
}

export async function persistPaymentPlanAssignments(
  athleteId: string,
  athleteTeams: any[],
  assignments: PaymentPlanAssignmentData[]
) {
  try {
    // Process assignments
    for (const assignment of assignments) {
      // Skip if this is an existing assignment that hasn't changed
      if (!assignment.id.startsWith('temp-')) {
        continue;
      }

      // Check if athlete is in the team
      const isAthleteInTeam = athleteTeams.some(team => team.teamId === assignment.teamId);
      
      // Add athlete to team if not already there
      if (!isAthleteInTeam && assignment.teamId) {
        await addAthleteToTeam(athleteId, assignment.teamId);
      }

      // Assign the payment plan
      if (assignment.isActive) {
        await assignPaymentPlan({
          athleteId,
          planId: assignment.planId,
          teamId: assignment.teamId || undefined,
        });
      }
    }

    // Handle deactivations - check for existing assignments that are now inactive
    // This would require comparing with the original state, which we'll handle in the client

    revalidatePath(`/athletes/${athleteId}`);
    revalidatePath(`/athletes/${athleteId}/edit`);
    
    return { success: true };
  } catch (error) {
    console.error("Error persisting payment plan assignments:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error" 
    };
  }
}

export async function persistPaymentPlanDeactivations(
  athleteId: string,
  deactivationIds: string[]
) {
  try {
    for (const assignmentId of deactivationIds) {
      // Skip temporary IDs
      if (assignmentId.startsWith('temp-')) {
        continue;
      }
      await deactivateAssignment(assignmentId);
    }

    revalidatePath(`/athletes/${athleteId}`);
    revalidatePath(`/athletes/${athleteId}/edit`);
    
    return { success: true };
  } catch (error) {
    console.error("Error deactivating payment plan assignments:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error" 
    };
  }
}
