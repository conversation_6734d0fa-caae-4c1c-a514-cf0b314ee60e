"use server";

import { athleteService, athleteTeamService, paymentPlanAssignmentService } from '../services';
import { getServerTenantId, getServerUserId } from '../tenant-utils-server';

export async function getAthleteTeams(athleteId: string) {
  const tenantId = await getServerTenantId();
  const userId = await getServerUserId();
  
  const result = await athleteService().getAthleteById(athleteId, userId?.toString() || undefined, tenantId || undefined);
  
  if (!result.success) {
    throw new Error(result.error?.message || `Failed to get teams for athlete with ID ${athleteId}`);
  }
  
  return result.data?.teamDetails || [];
}

export async function addAthleteToTeam(athleteId: string, teamId: string) {
  const tenantId = await getServerTenantId();
  const userId = await getServerUserId();
  
  const result = await athleteTeamService().addAthleteToTeam(
    athleteId, 
    teamId, 
    userId?.toString() || undefined, 
    tenantId || undefined
  );
  
  if (!result.success) {
    throw new Error(result.error?.message || 'Failed to add athlete to team');
  }
  
  return result.data;
}

export async function removeAthleteFromTeam(athleteId: string, teamId: string) {
  const tenantId = await getServerTenantId();
  const userId = await getServerUserId();
  
  const result = await athleteTeamService().removeAthleteFromTeam(
    athleteId, 
    teamId, 
    userId?.toString() || undefined, 
    tenantId || undefined
  );
  
  if (!result.success) {
    throw new Error(result.error?.message || 'Failed to remove athlete from team');
  }
  
  return result.data;
}

export async function getAthletePaymentPlansWithTeams(athleteId: string) {
  const tenantId = await getServerTenantId();
  const userId = await getServerUserId();
  
  const result = await paymentPlanAssignmentService().getAthleteAssignedPlans(
    athleteId, 
    userId?.toString() || undefined, 
    tenantId || undefined
  );
  
  if (!result.success) {
    throw new Error(result.error?.message || 'Failed to get athlete payment plans with teams');
  }
  
  return result.data;
}
