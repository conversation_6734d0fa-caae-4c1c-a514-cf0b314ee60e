"use server";

import { schoolService } from '../services';
import { getServerTenantId } from '../tenant-utils-server';

// Schools
export async function getSchools() {
  try {
    const tenantId = await getServerTenantId();
    const result = await schoolService().getSchools(undefined, tenantId || undefined);
    
    if (!result.success) {
      console.error("getSchools error:", result.error);
      throw new Error(result.error?.userMessage || "Failed to get schools");
    }
    
    return result.data || [];
  } catch (error) {
    console.error("getSchools error:", error);
    throw error;
  }
}

export async function getSchoolById(id: string) {
  try {
    const tenantId = await getServerTenantId();
    const result = await schoolService().getSchoolById(id, undefined, tenantId || undefined);
    
    if (!result.success) {
      console.error("getSchoolById error:", result.error);
      throw new Error(result.error?.userMessage || `Failed to get school with ID ${id}`);
    }
    
    return result.data;
  } catch (error) {
    console.error("getSchoolById error:", error);
    throw error;
  }
}

export async function updateSchool(id: string, data: { name?: string; address?: string; phone?: string; email?: string; logo?: string; foundedYear?: number; branches?: string[] }) {
  try {
    console.log("updateSchool called with:", { id, data });
    const tenantId = await getServerTenantId();
    
    // Use the service method that handles branches
    const result = await schoolService().updateSchoolWithBranches(
      id, 
      data, 
      undefined, 
      tenantId || undefined
    );
    
    if (!result.success) {
      console.error("updateSchool error:", result.error);
      throw new Error(result.error?.userMessage || `Failed to update school with ID ${id}`);
    }
    
    console.log("updateSchool result:", result.data);
    return result.data;
  } catch (error) {
    console.error("updateSchool error:", error);
    throw error;
  }
}

export async function createSchool(data: { name: string; foundedYear: number; address?: string; phone?: string; email?: string; logo?: string; branches?: string[] }) {
  try {
    console.log("createSchool called with:", data);
    const tenantId = await getServerTenantId();
    
    // Use the service method that handles branches
    const result = await schoolService().createSchoolWithBranches(
      data, 
      undefined, 
      tenantId || undefined
    );
    
    if (!result.success) {
      console.error("createSchool error:", result.error);
      throw new Error(result.error?.userMessage || "Failed to create school");
    }
    
    console.log("createSchool completed successfully");
    
    // Return a serializable success response
    return { success: true };
  } catch (error) {
    console.error("createSchool error:", error);
    throw error;
  }
}

export async function deleteSchool(id: string) {
  try {
    console.log("deleteSchool called with id:", id);
    const tenantId = await getServerTenantId();
    
    const result = await schoolService().deleteSchool(id, undefined, tenantId || undefined);
    
    if (!result.success) {
      console.error(`Failed to delete school with ID ${id}. deleteSchool error:`, result.error);
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }
      return { success: false, error: result.error?.userMessage || 'Failed to delete school' , errorType: 'general' };
    }else{
      console.log("deleteSchool result:", result.data);
      // Return a serializable success response
      return { success: true };
    }
  } catch (error : any) {
    console.error("deleteSchool error:", error);
    return { success: false, error: error.message };
  }
}