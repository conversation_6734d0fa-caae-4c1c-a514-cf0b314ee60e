"use server";

import { revalidatePath } from "next/cache";
import { athleteTeamService } from "../services/athlete-team.service";

export interface AddAthleteToTeamWithPaymentPlanParams {
  athleteId: string;
  teamId: string;
  paymentPlanId?: string;
  assignPaymentPlan?: boolean;
  useProrated?: boolean;
  customProratedAmount?: string;
  locale?: string;
}

export interface RemoveAthleteFromTeamParams {
  athleteId: string;
  teamId: string;
  removePaymentPlans?: boolean;
}

/**
 * Add an athlete to a team with optional payment plan assignment and prorated balance calculation
 */
export async function addAthleteToTeamWithPaymentPlan({
  athleteId,
  teamId,
  paymentPlanId,
  assignPaymentPlan: shouldAssignPaymentPlan = false,
  useProrated = false,
  customProratedAmount,
  locale = 'en'
}: AddAthleteToTeamWithPaymentPlanParams) {
  try {
    const service = athleteTeamService();
    const result = await service.addAthleteToTeamWithPaymentPlan({
      athleteId,
      teamId,
      paymentPlanId,
      assignPaymentPlan: shouldAssignPaymentPlan,
      useProrated,
      customProratedAmount,
      locale
    });

    if (!result.success) {
      console.error("addAthleteToTeamWithPaymentPlan error:", result.error);
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }else{
        return { success: false, error: result.error?.userMessage || "Failed to add athlete to team with payment plan" , errorType: 'general' };
      }
    }

    // Revalidate relevant paths
    revalidatePath(`/athletes/${athleteId}`);
    revalidatePath(`/teams/${teamId}`);
    revalidatePath('/athletes');
    revalidatePath('/teams');
    
    return { success: true };
  } catch (error) {
    console.error("Error adding athlete to team:", error);
    return { success: false, error: error instanceof Error ? error.message : "Failed to add athlete to team with payment plan", errorType: 'general' };
  }
}

/**
 * Remove an athlete from a team with optional payment plan cleanup
 */
export async function removeAthleteFromTeamWithCleanup({
  athleteId,
  teamId,
  removePaymentPlans = true
}: RemoveAthleteFromTeamParams) {
  try {
    const service = athleteTeamService();
    const result = await service.removeAthleteFromTeamWithCleanup({
      athleteId,
      teamId,
      removePaymentPlans
    });

    if (!result.success) {
      console.error("removeAthleteFromTeamWithCleanup error:", result.error);
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }else{
        return { success: false, error: result.error?.userMessage || "Failed to remove athlete from team with cleanup" , errorType: 'general' };
      }
    }

    // Revalidate relevant paths
    revalidatePath(`/athletes/${athleteId}`);
    revalidatePath(`/teams/${teamId}`);
    revalidatePath('/athletes');
    revalidatePath('/teams');
    
    return { success: true };
  } catch (error) {
    console.error("Error removing athlete from team:", error);
    return { success: false, error: error instanceof Error ? error.message : "Failed to remove athlete from team with cleanup", errorType: 'general' };
  }
}

/**
 * Get available payment plans for a team (filtered by team's branch)
 */
export async function getAvailablePaymentPlansForTeam(teamBranchId: string) {
  try {
    const service = athleteTeamService();
    const result = await service.getAvailablePaymentPlansForTeam(teamBranchId);

    if (!result.success) {
      console.error("getAvailablePaymentPlansForTeam error:", result.error);
      throw new Error(result.error?.userMessage || "Failed to get available payment plans for team");
    }

    return result.data || [];
  } catch (error) {
    console.error("Error getting available payment plans for team:", error);
    throw error;
  }
}

/**
 * Check if athlete is already in team
 */
export async function checkAthleteInTeam(athleteId: string, teamId: string): Promise<boolean> {
  try {
    const service = athleteTeamService();
    const result = await service.checkAthleteInTeam(athleteId, teamId);

    if (!result.success) {
      console.error("checkAthleteInTeam error:", result.error);
      throw new Error(result.error?.userMessage || "Failed to check if athlete is in team");
    }

    return result.data ?? false;
  } catch (error) {
    console.error("Error checking athlete in team:", error);
    throw error;
  }
}

/**
 * Get athlete's active payment plans for a specific team
 */
export async function getAthleteTeamPaymentPlans(athleteId: string, teamId: string) {
  try {
    const service = athleteTeamService();
    const result = await service.getAthleteTeamPaymentPlans(athleteId, teamId);

    if (!result.success) {
      console.error("getAthleteTeamPaymentPlans error:", result.error);
      throw new Error(result.error?.userMessage || "Failed to get athlete team payment plans");
    }

    return result.data || [];
  } catch (error) {
    console.error("Error getting athlete team payment plans:", error);
    throw error;
  }
}
