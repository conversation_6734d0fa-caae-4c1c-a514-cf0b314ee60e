import { 
  ErrorCodes, 
  ErrorSeverity, 
  ServiceError, 
  ErrorContext,
  ValidationError,
  ServiceResult 
} from './types';

/**
 * Custom error classes for the sports club management system
 */

export class AppError extends Error {
  public readonly code: ErrorCodes;
  public readonly severity: ErrorSeverity;
  public readonly context?: ErrorContext;
  public readonly details?: Record<string, any>;
  public readonly userMessage?: string;
  public readonly retryable: boolean;

  constructor(
    code: ErrorCodes,
    message: string,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context?: ErrorContext,
    details?: Record<string, any>,
    userMessage?: string,
    retryable: boolean = false
  ) {
    super(message);
    this.name = 'AppError';
    this.code = code;
    this.severity = severity;
    this.context = context;
    this.details = details;
    this.userMessage = userMessage;
    this.retryable = retryable;

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
  }

  toServiceError(): ServiceError {
    return {
      code: this.code,
      message: this.message,
      severity: this.severity,
      context: this.context,
      cause: this,
      details: this.details,
      userMessage: this.userMessage,
      retryable: this.retryable,
    };
  }
}

export class ValidationAppError extends AppError {
  public readonly validationErrors: ValidationError[];

  constructor(
    validationErrors: ValidationError[],
    message: string = 'Validation failed',
    context?: ErrorContext
  ) {
    super(
      ErrorCodes.VALIDATION_ERROR,
      message,
      ErrorSeverity.LOW,
      context,
      { validationErrors },
      'Please check the provided information and try again.'
    );
    this.name = 'ValidationAppError';
    this.validationErrors = validationErrors;
  }
}

export class NotFoundError extends AppError {
  constructor(
    resource: string,
    identifier?: string,
    context?: ErrorContext
  ) {
    const message = identifier 
      ? `${resource} with identifier '${identifier}' not found`
      : `${resource} not found`;
    
    super(
      ErrorCodes.RESOURCE_NOT_FOUND,
      message,
      ErrorSeverity.LOW,
      context,
      { resource, identifier },
      `The requested ${resource.toLowerCase()} could not be found.`
    );
    this.name = 'NotFoundError';
  }
}

export class UnauthorizedError extends AppError {
  constructor(
    message: string = 'Unauthorized access',
    context?: ErrorContext
  ) {
    super(
      ErrorCodes.UNAUTHORIZED,
      message,
      ErrorSeverity.HIGH,
      context,
      undefined,
      'You are not authorized to perform this action.'
    );
    this.name = 'UnauthorizedError';
  }
}

export class ForbiddenError extends AppError {
  constructor(
    message: string = 'Access forbidden',
    context?: ErrorContext
  ) {
    super(
      ErrorCodes.FORBIDDEN,
      message,
      ErrorSeverity.HIGH,
      context,
      undefined,
      'You do not have permission to access this resource.'
    );
    this.name = 'ForbiddenError';
  }
}

export class BusinessRuleError extends AppError {
  constructor(
    rule: string,
    message: string,
    context?: ErrorContext,
    userMessage?: string
  ) {
    super(
      ErrorCodes.BUSINESS_RULE_VIOLATION,
      message,
      ErrorSeverity.MEDIUM,
      context,
      { rule },
      userMessage || 'This operation violates business rules.'
    );
    this.name = 'BusinessRuleError';
  }
}

export class DatabaseError extends AppError {
  constructor(
    message: string,
    cause?: Error,
    context?: ErrorContext,
    retryable: boolean = false
  ) {
    super(
      ErrorCodes.DATABASE_ERROR,
      message,
      ErrorSeverity.HIGH,
      context,
      { originalError: cause?.message },
      'A database error occurred. Please try again later.',
      retryable
    );
    this.name = 'DatabaseError';
    this.cause = cause;
  }
}

export class ExternalServiceError extends AppError {
  constructor(
    service: string,
    message: string,
    cause?: Error,
    context?: ErrorContext,
    retryable: boolean = true
  ) {
    super(
      ErrorCodes.EXTERNAL_SERVICE_ERROR,
      `${service}: ${message}`,
      ErrorSeverity.MEDIUM,
      context,
      { service, originalError: cause?.message },
      'An external service is temporarily unavailable. Please try again later.',
      retryable
    );
    this.name = 'ExternalServiceError';
    this.cause = cause;
  }
}

/**
 * Error utility functions
 */

export function createServiceResult<T>(
  data?: T,
  error?: ServiceError,
  validationErrors?: ValidationError[],
  metadata?: Record<string, any>
): ServiceResult<T> {
  return {
    success: !error && !validationErrors?.length,
    data,
    error,
    validationErrors,
    metadata,
  };
}

export function createSuccessResult<T>(
  data: T,
  metadata?: Record<string, any>
): ServiceResult<T> {
  return createServiceResult(data, undefined, undefined, metadata);
}

export function createErrorResult<T>(
  error: ServiceError
): ServiceResult<T> {
  return createServiceResult<T>(undefined, error);
}

export function createValidationErrorResult<T>(
  validationErrors: ValidationError[]
): ServiceResult<T> {
  return createServiceResult<T>(undefined, undefined, validationErrors);
}

export function isAppError(error: any): error is AppError {
  return error instanceof AppError;
}

export function handleUnknownError(
  error: unknown,
  context?: ErrorContext,
  fallbackMessage: string = 'An unexpected error occurred'
): ServiceError {
  if (isAppError(error)) {
    return error.toServiceError();
  }

  if (error instanceof Error) {
    return new AppError(
      ErrorCodes.INTERNAL_SERVER_ERROR,
      error.message,
      ErrorSeverity.HIGH,
      context,
      { originalError: error.message },
      fallbackMessage
    ).toServiceError();
  }

  return new AppError(
    ErrorCodes.INTERNAL_SERVER_ERROR,
    typeof error === 'string' ? error : fallbackMessage,
    ErrorSeverity.HIGH,
    context,
    { originalError: String(error) },
    fallbackMessage
  ).toServiceError();
}
