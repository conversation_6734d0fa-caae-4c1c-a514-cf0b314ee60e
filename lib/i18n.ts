import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import Backend from 'i18next-http-backend';
import LanguageDetector from 'i18next-browser-languagedetector';

const isServer = typeof window === 'undefined';

// Initialize i18n instance
const i18nInstance = i18n.createInstance();

// Only configure on client side to avoid SSR issues
if (!isServer) {
  i18nInstance
    .use(Backend)
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
      lng: 'en',
      fallbackLng: 'en',
      supportedLngs: ['en', 'tr'],
      ns: ['shared', 'auth', 'dashboard', 'athletes', 'instructors', 'teams', 'schools', 'facilities', 'items', 'expenses', 'payments', 'sms','errors'],
      defaultNS: 'shared',
      debug: false,
      interpolation: {
        escapeValue: false,
      },
      backend: {
        loadPath: '/locales/{{lng}}/{{ns}}.json',
      },
      detection: {
        order: ['cookie', 'localStorage', 'navigator'],
        caches: ['cookie'],
      },
      react: {
        useSuspense: false,
      },
    });
} else {
  // Server-side fallback configuration
  i18nInstance
    .use(initReactI18next)
    .init({
      lng: 'en',
      fallbackLng: 'en',
      supportedLngs: ['en', 'tr'],
      resources: {
        en: {
          shared: {},
          auth: {},
          dashboard: {},
          athletes: {},
          instructors: {},
          teams: {},
          schools: {},
          facilities: {},
          items: {},
          expenses: {},
          payments: {},
          sms: {},
          errors: {},
        },
        tr: {
          shared: {},
          auth: {},
          dashboard: {},
          athletes: {},
          instructors: {},
          teams: {},
          schools: {},
          facilities: {},
          items: {},
          expenses: {},
          payments: {},
          sms: {},
          errors: {},
        },
      },
      react: {
        useSuspense: false,
      },
    });
}

export default i18nInstance;