/**
 * Image Cache Utilities
 * 
 * Provides utility functions for managing the global image cache.
 * Useful for debugging, cache management, and performance monitoring.
 */

import { imageCache } from './image-cache';

/**
 * Clear all cached images for the current tenant
 */
export function clearTenantImageCache(tenantId: string): void {
  imageCache.invalidateTenant(tenantId);
}

/**
 * Clear all cached images
 */
export function clearAllImageCache(): void {
  imageCache.clear();
}

/**
 * Get cache statistics
 */
export function getImageCacheStats(): { size: number; maxSize: number; maxAge: number } {
  return imageCache.getStats();
}

/**
 * Invalidate a specific image from cache
 */
export function invalidateImage(src: string, tenantId: string): void {
  imageCache.invalidate(src, tenantId);
}

/**
 * Check if an image is cached
 */
export function isImageCached(src: string, tenantId: string): boolean {
  return imageCache.get(src, tenantId) !== null;
}

/**
 * Preload images into cache (useful for performance optimization)
 */
export async function preloadImages(
  images: Array<{ src: string; tenantId: string }>,
  options: { signal?: AbortSignal } = {}
): Promise<void> {
  const { signal } = options;

  const loadPromises = images.map(async ({ src, tenantId }) => {
    try {
      // Skip if already cached
      if (isImageCached(src, tenantId)) {
        return;
      }

      // Skip if not a secure image
      if (!src.includes('/api/secure-images/')) {
        return;
      }

      if (signal?.aborted) {
        return;
      }

      const response = await fetch(src, {
        signal,
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      if (signal?.aborted) {
        return;
      }

      if (!response.ok) {
        console.warn(`Failed to preload image ${src}: ${response.status}`);
        return;
      }

      const blob = await response.blob();

      if (signal?.aborted) {
        return;
      }

      imageCache.set(src, tenantId, blob);
    } catch (error) {
      if (signal?.aborted) {
        return;
      }
      console.warn(`Failed to preload image ${src}:`, error);
    }
  });

  await Promise.allSettled(loadPromises);
}

/**
 * Debug function to log cache contents (development only)
 */
export function debugImageCache(): void {
  if (process.env.NODE_ENV !== 'development') {
    console.warn('debugImageCache() is only available in development mode');
    return;
  }

  const stats = getImageCacheStats();
  console.group('🖼️ Image Cache Debug');
  console.log('Cache Stats:', stats);
  
  // Note: We can't easily log the actual cache contents without exposing
  // the internal cache structure, which would break encapsulation.
  // The stats provide sufficient debugging information.
  
  console.groupEnd();
}

/**
 * Hook for React DevTools or debugging
 * Provides cache information that can be inspected
 */
export function useImageCacheDebug() {
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return {
    stats: getImageCacheStats(),
    clearAll: clearAllImageCache,
    clearTenant: clearTenantImageCache,
    invalidate: invalidateImage,
    isImageCached,
    preloadImages,
    debug: debugImageCache,
  };
}
