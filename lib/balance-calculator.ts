"use server";

import { db } from "@/src/db";
import { athletes, payments } from "@/src/db/schema";
import { eq, and, sum, sql } from "drizzle-orm";

/**
 * Calculate athlete balance based on payments
 * Balance = Total Outstanding (Non-Completed) Payments  
 * Always negative or zero (athletes owe money or are fully paid)
 * Negative balance = athlete owes money
 * Zero balance = athlete is fully paid up
 */
export async function calculateAthleteBalance(athleteId: string, tenantId: string): Promise<number> {
  // Get all non-completed payments for this athlete (pending, overdue)
  const outstandingPayments = await db
    .select({
      totalAmount: sum(sql`CAST(${payments.amount} AS DECIMAL(10,2))`),
    })
    .from(payments)
    .where(
      and(
        eq(payments.athleteId, athleteId),
        eq(payments.tenantId, tenantId),
        // Only include non-completed payments (what they still owe)
        sql`${payments.status} IN ('pending', 'overdue')`
      )
    )
    .groupBy(payments.athleteId);

  // Calculate balance (negative = owes money, zero = fully paid)
  const totalOutstanding = Number(outstandingPayments[0]?.totalAmount || 0);
  
  // Return negative balance (money owed) - zero if no outstanding payments
  return totalOutstanding > 0 ? -totalOutstanding : 0;
}

/**
 * Update an athlete's balance in the database
 */
export async function updateAthleteBalance(athleteId: string, tenantId: string, userId?: bigint): Promise<number> {
  const calculatedBalance = await calculateAthleteBalance(athleteId, tenantId);
  
  await db
    .update(athletes)
    .set({
      balance: calculatedBalance.toFixed(2),
      updatedAt: new Date(),
      ...(userId && { updatedBy: userId }),
    })
    .where(
      and(
        eq(athletes.id, athleteId),
        eq(athletes.tenantId, tenantId)
      )
    );

  return calculatedBalance;
}

/**
 * Get detailed balance breakdown for an athlete
 */
export async function getAthleteBalanceBreakdown(athleteId: string, tenantId: string) {
  const paymentBreakdown = await db
    .select({
      status: payments.status,
      count: sql<number>`COUNT(*)`,
      total: sum(sql`CAST(${payments.amount} AS DECIMAL(10,2))`),
    })
    .from(payments)
    .where(
      and(
        eq(payments.athleteId, athleteId),
        eq(payments.tenantId, tenantId)
      )
    )
    .groupBy(payments.status);

  const breakdown = {
    completed: { count: 0, total: 0 },
    pending: { count: 0, total: 0 },
    overdue: { count: 0, total: 0 },
    cancelled: { count: 0, total: 0 },
  };

  paymentBreakdown.forEach(item => {
    if (item.status in breakdown) {
      breakdown[item.status as keyof typeof breakdown] = {
        count: Number(item.count),
        total: parseFloat(item.total || "0"),
      };
    }
  });

  const calculatedBalance = breakdown.pending.total + breakdown.overdue.total;
  // Return negative balance (money owed) - zero if no outstanding payments
  const finalBalance = calculatedBalance > 0 ? -calculatedBalance : 0;

  return {
    breakdown,
    calculatedBalance: finalBalance,
    totalPayments: Object.values(breakdown).reduce((sum, item) => sum + item.count, 0),
    totalAmount: Object.values(breakdown).reduce((sum, item) => sum + item.total, 0),
  };
}
