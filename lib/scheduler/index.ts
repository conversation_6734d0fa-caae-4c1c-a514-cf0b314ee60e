import PaymentScheduler from './payment-scheduler';

let scheduler: PaymentScheduler | null = null;
let isInitialized = false;

export function initializeScheduler() {
  if (isInitialized) {
    console.log('⚠️  Payment scheduler already initialized');
    return scheduler;
  }

  scheduler = PaymentScheduler.getInstance();
  scheduler.start();
  isInitialized = true;
  
  console.log('✅ Payment scheduler initialized and started');
  return scheduler;
}

export function getScheduler(): PaymentScheduler | null {
  return scheduler;
}

export function stopScheduler() {
  if (scheduler) {
    scheduler.stop();
    scheduler = null;
    isInitialized = false;
    console.log('🛑 Payment scheduler stopped and cleaned up');
  }
}
