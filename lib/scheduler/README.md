# Payment Scheduler

This module provides automatic payment processing for the sports club management system.

## Overview

The payment scheduler automatically creates pending payments for athletes based on their active payment plan assignments. It runs as a background task using cron expressions.

## Features

- **Automatic Payment Creation**: Creates pending payments for athletes on their assigned payment dates
- **Retry Mechanism**: Automatically retries failed operations with configurable delays
- **Team-based Plans**: Supports both general and team-specific payment plans
- **Graceful Shutdown**: <PERSON><PERSON><PERSON> handles application shutdown signals
- **Status Tracking**: Only processes active athletes with active payment plans
- **Manual Execution**: CLI script for manual payment processing when needed
- **Smart Duplicate Prevention**: Prevents double-charging athletes with intelligent duplicate detection

## Smart Duplicate Prevention

The payment processor includes intelligent duplicate detection to prevent double-charging athletes:

- **Monthly Payment Checking**: Before creating a new payment, the system checks if any payment already exists for the athlete's specific payment plan assignment for the current month
- **Assignment Date Change Handling**: If a payment plan's assignment day is changed after payments have been created (e.g., changed from 5th to 10th of the month), the system will not create duplicate payments
- **All Payment Status Check**: The duplicate check considers payments with any status (pending, paid, cancelled), not just pending payments
- **Date Range Protection**: Uses month boundaries to ensure payments are only created once per month per assignment

### Example Scenario
```
1. Payment plan assigned on 5th day of month
2. Payment created on 5th day for June 2025
3. Plan assignment day changed to 10th day
4. Scheduler runs again on 10th day
5. System detects existing payment for June 2025 and skips creation
6. No duplicate payment is created
```

This feature is crucial for preventing billing errors when payment plan configurations are updated after payments have already been processed.

## Configuration

The scheduler can be configured using environment variables:

### Required Environment Variables

None - the scheduler will work with default values.

### Optional Environment Variables

```bash
# Cron expression for when to run the scheduler (default: midnight every day)
PAYMENT_CRON_SCHEDULE="0 0 * * *"

# Maximum number of retry attempts (default: 3)
PAYMENT_SCHEDULER_MAX_RETRIES="3"

# Delay between retries in milliseconds (default: 1800000 = 30 minutes)
PAYMENT_SCHEDULER_RETRY_DELAY_MS="1800000"
```

## Cron Expression Examples

```bash
# Every day at midnight
PAYMENT_CRON_SCHEDULE="0 0 * * *"

# Every day at 2 AM
PAYMENT_CRON_SCHEDULE="0 2 * * *"

# Every hour
PAYMENT_CRON_SCHEDULE="0 * * * *"

# Every 30 minutes
PAYMENT_CRON_SCHEDULE="*/30 * * * *"

# Every Monday at 9 AM
PAYMENT_CRON_SCHEDULE="0 9 * * 1"
```

## Manual Payment Processing

When the scheduled task fails or you need to process payments manually, you can use the CLI script:

### Using npm scripts (Recommended)

```bash
# Get help and see all available options
npm run process-payments:help

# Process payments for today
npm run process-payments

# Process payments for a specific date
npm run process-payments 2025-06-20
```

### Using tsx directly

```bash
# Get help and see all available options
npx tsx scripts/process-payments.ts --help

# Process payments for today
npx tsx scripts/process-payments.ts

# Process payments for a specific date
npx tsx scripts/process-payments.ts 2025-06-20
```

### Script Features

- **Help System**: Built-in help with `--help` flag showing detailed usage instructions
- **Date Validation**: Ensures date format is YYYY-MM-DD and validates date values
- **Detailed Logging**: Shows progress, results, and any errors with emojis for clarity
- **Error Handling**: Graceful error handling with proper exit codes and helpful messages
- **Signal Handling**: Responds to SIGINT/SIGTERM for clean shutdown
- **Results Summary**: Shows total assignments processed, payments created, and error count
- **Development Mode**: Shows stack traces in development for debugging

## Deployment Architecture

The payment scheduler is designed to run only in production server environments and not during the build process:

### Initialization Strategy
- **Instrumentation Hook**: Uses Next.js `instrumentation.ts` for proper server startup initialization
- **Build Isolation**: The scheduler is not initialized during `npm run build` to avoid unnecessary resource usage and build issues
- **Single Instance**: Global flag prevents multiple scheduler instances from running simultaneously
- **Runtime Only**: Dynamic imports ensure database connections only occur at runtime, not during build

### Environment Behavior
- **Production**: Scheduler automatically starts when the server boots up
- **Development**: Scheduler can be manually initialized if needed
- **Build Time**: No scheduler initialization occurs during static generation or build processes

This approach ensures clean builds and proper resource management while maintaining reliable payment processing in production.

- **Scheduled task failed**: When automatic processing fails after all retries
- **System downtime**: Process missed payments after system was down
- **Historical processing**: Process payments for specific past dates
- **Testing**: Validate payment processing logic in different environments
- **Manual recovery**: Fix payment issues after system problems
- **Maintenance**: Process payments during maintenance windows

### Example Output

```bash
$ npm run process-payments 2025-06-20

🚀 Starting manual payment processing...
📅 Target date: 2025-06-20
🕒 Started at: 2025-06-20T10:30:00.000Z
──────────────────────────────────────────────────
💰 Processing payments for specific date: 2025-06-20
──────────────────────────────────────────────────
✅ Payment processing completed successfully!
📊 Results:
   • Total assignments processed: 25
   • Payments created: 23
   • Errors encountered: 0
🕒 Completed at: 2025-06-20T10:30:05.123Z
──────────────────────────────────────────────────
```

### When to Use Manual Processing

- **Scheduled Task Failure**: When the automatic scheduler fails after all retries
- **Maintenance Windows**: Process missed payments after system downtime
- **Testing**: Verify payment logic with specific dates
- **Recovery**: Process payments for past dates if needed
- **Troubleshooting**: Debug payment processing issues

## How It Works

1. **Startup**: The scheduler initializes automatically when the application starts
2. **Scheduled Execution**: Runs according to the cron expression (default: midnight daily)
3. **Payment Processing**: 
   - Finds active payment plan assignments
   - Checks if payments should be created based on:
     - New assignments (assigned today)
     - Recurring payments (assign day matches current day of month)
   - Creates pending payments with appropriate due dates
   - Only processes active athletes with active payment plans
4. **Retry Logic**: If processing fails, it retries up to the configured maximum with delays
5. **Logging**: Provides detailed logging of all operations

## Payment Creation Logic

Payments are created when:
- An athlete has an active payment plan assignment
- The payment plan is active
- The athlete is active (not inactive or suspended)
- Either:
  - The assignment was created today, OR
  - Today's day of the month matches the payment plan's assign day
- No pending payment already exists for this assignment

## TODO: Multi-Instance Support

Currently, the scheduler runs on a single instance. For production environments with multiple instances, implement Redis-based locking:

```typescript
// TODO: Add Redis lock before processing
const lockKey = `payment-scheduler-lock-${new Date().toISOString().split('T')[0]}`;
const lock = await redis.set(lockKey, 'processing', 'EX', 3600, 'NX');
if (!lock) {
  console.log('Another instance is already processing payments');
  return;
}
```

## Development

To test the scheduler manually, you can trigger it by restarting the development server or by directly calling the payment processor functions in a development environment.

## Monitoring

The scheduler provides comprehensive logging with emojis for easy identification:
- 🚀 Startup messages
- ✅ Success operations
- ❌ Error conditions
- ⚠️ Warning messages
- 📅 Schedule information
- 💰 Payment processing
- 🔄 Retry attempts
- 🛑 Shutdown events
