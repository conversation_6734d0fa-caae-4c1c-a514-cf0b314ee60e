// Utility functions for schedule management

export interface FacilitySchedule {
  facilityId: string;
  facilityName: string;
  scheduleId: string;
  teamId: string;
  teamName: string;
  dayOfWeek: number;
  startTime: string;
  endTime: string;
}

export interface TimeSlot {
  start: string;
  end: string;
  isAvailable: boolean;
  occupiedBy?: string;
}

export interface DaySchedule {
  dayOfWeek: number;
  timeSlots: TimeSlot[];
}

/**
 * Convert time string to minutes since midnight for easier comparison
 */
export function timeToMinutes(timeStr: string): number {
  const [hours, minutes] = timeStr.split(':').map(Number);
  return hours * 60 + minutes;
}

/**
 * Convert minutes since midnight back to time string
 */
export function minutesToTime(minutes: number): string {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
}

/**
 * Check if two time ranges overlap
 */
export function timeRangesOverlap(
  start1: string, 
  end1: string, 
  start2: string, 
  end2: string
): boolean {
  const start1Min = timeToMinutes(start1);
  const end1Min = timeToMinutes(end1);
  const start2Min = timeToMinutes(start2);
  const end2Min = timeToMinutes(end2);

  return start1Min < end2Min && start2Min < end1Min;
}

/**
 * Generate available time slots for a facility on a specific day
 */
export function generateAvailableTimeSlots(
  facilitySchedules: FacilitySchedule[],
  facilityId: string,
  dayOfWeek: number,
  startHour: number = 6,
  endHour: number = 22,
  slotDuration: number = 60 // minutes
): TimeSlot[] {
  const slots: TimeSlot[] = [];
  const daySchedules = facilitySchedules.filter(
    s => s.facilityId === facilityId && s.dayOfWeek === dayOfWeek
  );

  // Generate hourly slots
  for (let hour = startHour; hour < endHour; hour++) {
    const startTime = minutesToTime(hour * 60);
    const endTime = minutesToTime((hour + 1) * 60);
    
    // Check if this slot conflicts with any existing schedule
    const conflict = daySchedules.find(schedule => 
      timeRangesOverlap(startTime, endTime, schedule.startTime, schedule.endTime)
    );

    slots.push({
      start: startTime,
      end: endTime,
      isAvailable: !conflict,
      occupiedBy: conflict?.teamName
    });
  }

  return slots;
}

/**
 * Get facility availability for all days of the week
 */
export function getFacilityWeeklyAvailability(
  facilitySchedules: FacilitySchedule[],
  facilityId: string
): DaySchedule[] {
  const weekSchedule: DaySchedule[] = [];

  for (let day = 1; day <= 7; day++) {
    weekSchedule.push({
      dayOfWeek: day,
      timeSlots: generateAvailableTimeSlots(facilitySchedules, facilityId, day)
    });
  }

  return weekSchedule;
}

/**
 * Check if a proposed schedule conflicts with existing schedules
 */
export function checkScheduleConflict(
  facilitySchedules: FacilitySchedule[],
  facilityId: string,
  dayOfWeek: number,
  startTime: string,
  endTime: string,
  excludeTeamId?: string
): { hasConflict: boolean; conflictingTeam?: string } {
  if (!facilityId || !startTime || !endTime) {
    return { hasConflict: false };
  }

  const conflicts = facilitySchedules.filter(schedule => 
    schedule.facilityId === facilityId &&
    schedule.dayOfWeek === dayOfWeek &&
    schedule.teamId !== excludeTeamId &&
    timeRangesOverlap(startTime, endTime, schedule.startTime, schedule.endTime)
  );

  return {
    hasConflict: conflicts.length > 0,
    conflictingTeam: conflicts[0]?.teamName
  };
}

export const dayNames = {
  1: 'monday',
  2: 'tuesday', 
  3: 'wednesday',
  4: 'thursday',
  5: 'friday',
  6: 'saturday',
  7: 'sunday'
} as const;
