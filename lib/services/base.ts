import {
  ServiceResult,
  ServiceError,
  Error<PERSON>ontext,
  ValidationError
} from '../errors/types';
import {
  AppError,
  NotFoundError,
  createSuccessResult,
  createErrorResult,
  createValidationErrorResult,
  handleUnknownError
} from '../errors/errors';
import { getServerTenantId } from '../tenant-utils-server';

/**
 * Base service class that provides common functionality for all services
 */
export abstract class BaseService {
  protected readonly serviceName: string;

  constructor(serviceName: string) {
    this.serviceName = serviceName;
  }

  /**
   * Creates error context with service information
   */
  protected createContext(
    operation: string,
    resource?: string,
    userId?: string,
    tenantId?: string,
    metadata?: Record<string, any>
  ): ErrorContext {
    return {
      userId,
      tenantId,
      operation: `${this.serviceName}.${operation}`,
      resource,
      requestId: this.generateRequestId(),
      timestamp: new Date(),
      metadata,
    };
  }

  /**
   * Generates a unique request ID for tracking
   */
  private generateRequestId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Executes an operation with error handling
   */
  protected async executeOperation<T>(
    operation: string,
    fn: () => Promise<T>,
    context?: Partial<ErrorContext>
  ): Promise<ServiceResult<T>> {
    const fullContext = this.createContext(
      operation,
      context?.resource,
      context?.userId,
      context?.tenantId,
      context?.metadata
    );

    try {
      const result = await fn();
      return createSuccessResult(result);
    } catch (error) {
      const serviceError = handleUnknownError(error, fullContext);
      this.logError(serviceError, fullContext);
      return createErrorResult<T>(serviceError);
    }
  }

  /**
   * Validates data and returns validation errors if any
   */
  protected validate<T>(
    data: T,
    validators: Array<(data: T) => ValidationError | null>
  ): ValidationError[] {
    const errors: ValidationError[] = [];
    
    for (const validator of validators) {
      const error = validator(data);
      if (error) {
        errors.push(error);
      }
    }
    
    return errors;
  }

  /**
   * Executes an operation with validation
   */
  protected async executeWithValidation<TInput, TOutput>(
    operation: string,
    data: TInput,
    validators: Array<(data: TInput) => ValidationError | null>,
    fn: (data: TInput) => Promise<TOutput>,
    context?: Partial<ErrorContext>
  ): Promise<ServiceResult<TOutput>> {
    const fullContext = this.createContext(
      operation,
      context?.resource,
      context?.userId,
      context?.tenantId,
      context?.metadata
    );

    // Validate input
    const validationErrors = this.validate(data, validators);
    if (validationErrors.length > 0) {
      return createValidationErrorResult<TOutput>(validationErrors);
    }

    try {
      const result = await fn(data);
      return createSuccessResult(result);
    } catch (error) {
      const serviceError = handleUnknownError(error, fullContext);
      this.logError(serviceError, fullContext);
      return createErrorResult<TOutput>(serviceError);
    }
  }

  /**
   * Logs errors with appropriate level based on severity
   */
  private logError(error: ServiceError, context: ErrorContext): void {
    const logData = {
      error,
      context,
      timestamp: new Date().toISOString(),
      service: this.serviceName,
    };

    switch (error.severity) {
      case 'CRITICAL':
        console.error('CRITICAL ERROR:', logData);
        break;
      case 'HIGH':
        console.error('HIGH SEVERITY ERROR:', logData);
        break;
      case 'MEDIUM':
        console.warn('MEDIUM SEVERITY ERROR:', logData);
        break;
      case 'LOW':
        console.info('LOW SEVERITY ERROR:', logData);
        break;
      default:
        console.log('ERROR:', logData);
    }
  }

  /**
   * Utility method to check if a resource exists
   */
  protected async checkResourceExists<T>(
    operation: string,
    resourceName: string,
    identifier: string,
    fetchFn: () => Promise<T | null>,
    context?: Partial<ErrorContext>
  ): Promise<ServiceResult<T>> {
    const fullContext = this.createContext(
      operation,
      resourceName,
      context?.userId,
      context?.tenantId,
      { identifier, ...context?.metadata }
    );

    try {
      const resource = await fetchFn();
      if (!resource) {
        const error = new AppError(
          'RESOURCE_NOT_FOUND' as any,
          `${resourceName} with identifier '${identifier}' not found`,
          'LOW' as any,
          fullContext,
          { resourceName, identifier },
          `The requested ${resourceName.toLowerCase()} could not be found.`
        ).toServiceError();
        
        return createErrorResult<T>(error);
      }
      return createSuccessResult(resource);
    } catch (error) {
      const serviceError = handleUnknownError(error, fullContext);
      this.logError(serviceError, fullContext);
      return createErrorResult<T>(serviceError);
    }
  }

  /**
   * Handles database operations with proper error mapping
   */
  protected async executeDatabaseOperation<T>(
    operation: string,
    fn: () => Promise<T>,
    context?: Partial<ErrorContext>
  ): Promise<ServiceResult<T>> {
    const fullContext = this.createContext(
      operation,
      context?.resource,
      context?.userId,
      context?.tenantId,
      context?.metadata
    );

    try {
      const result = await fn();
      return createSuccessResult(result);
    } catch (error) {
      let serviceError: ServiceError;
      
      if (error instanceof Error) {
        // Map database-specific errors
        if (error.message.includes('UNIQUE constraint')) {
          serviceError = new AppError(
            'DUPLICATE_ENTRY' as any,
            'A record with this information already exists',
            'MEDIUM' as any,
            fullContext,
            { originalError: error.message },
            'This information is already in use. Please try different values.'
          ).toServiceError();
        } else if (error.message.includes('FOREIGN KEY constraint')) {
          serviceError = new AppError(
            'FOREIGN_KEY_CONSTRAINT' as any,
            'Cannot perform operation due to related records',
            'MEDIUM' as any,
            fullContext,
            { originalError: error.message },
            'This operation cannot be completed because other records depend on this data.'
          ).toServiceError();
        } else {
          serviceError = handleUnknownError(error, fullContext);
        }
      } else {
        serviceError = handleUnknownError(error, fullContext);
      }
      
      this.logError(serviceError, fullContext);
      return createErrorResult<T>(serviceError);
    }
  }

  /**
   * Generic delete operation with existence validation
   */
  protected async deleteWithValidation<TResult>(
    operation: string,
    id: string,
    deleteFn: (id: string, tenantId: string) => Promise<TResult>,
    getByIdFn: (id: string, tenantId: string) => Promise<any>,
    resourceName: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<TResult>> {
    return this.executeOperation(
      operation,
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();

        // Check if resource exists
        const existing = await getByIdFn(id, effectiveTenantId || '');
        if (!existing) {
          throw new NotFoundError(`${resourceName} not found`);
        }

        return deleteFn(id, effectiveTenantId || '');
      },
      {
        userId,
        tenantId,
        resource: resourceName.toLowerCase(),
      }
    );
  }
}
