import { BaseService } from './base';
import { ServiceResult } from '../errors/types';
import { 
  assignPaymentPlanToAthlete, 
  getAthletePaymentPlans, 
  getPaymentPlanAssignmentCounts,
  deactivatePaymentPlanAssignment,
  reactivatePaymentPlanAssignment,
  deletePaymentPlanAssignment,
  createScheduledPayments,
  updateOverduePayments,
  AssignPaymentPlanParams
} from '../payment-plan-utils';

export interface AssignPaymentPlanServiceParams {
  athleteId: string;
  planId: string;
  teamId?: string;
  isActive?: boolean;
}

export class PaymentPlanAssignmentService extends BaseService {
  constructor() {
    super('PaymentPlanAssignmentService');
  }

  /**
   * Assign a payment plan to an athlete
   */
  async assignPaymentPlan(
    params: AssignPaymentPlanServiceParams,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'assignPaymentPlan',
      async () => {
        if (!userId || !tenantId) {
          throw new Error('Missing user or tenant context');
        }

        const assignParams: AssignPaymentPlanParams = {
          athleteId: params.athleteId,
          planId: params.planId,
          teamId: params.teamId,
          tenantId,
          userId: BigInt(userId),
          isActive: params.isActive ?? true
        };

        return await assignPaymentPlanToAthlete(assignParams);
      },
      { userId, tenantId }
    );
  }

  /**
   * Get all payment plans assigned to an athlete
   */
  async getAthleteAssignedPlans(
    athleteId: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getAthleteAssignedPlans',
      async () => {
        if (!tenantId) {
          throw new Error('Missing tenant context');
        }

        return await getAthletePaymentPlans(athleteId, tenantId);
      },
      { userId, tenantId }
    );
  }

  /**
   * Get payment plan assignment statistics
   */
  async getPaymentPlanStats(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getPaymentPlanStats',
      async () => {
        if (!tenantId) {
          throw new Error('Missing tenant context');
        }

        return await getPaymentPlanAssignmentCounts(tenantId);
      },
      { userId, tenantId }
    );
  }

  /**
   * Deactivate a payment plan assignment
   */
  async deactivateAssignment(
    assignmentId: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<void>> {
    return this.executeOperation(
      'deactivateAssignment',
      async () => {
        if (!userId || !tenantId) {
          throw new Error('Missing user or tenant context');
        }

        await deactivatePaymentPlanAssignment(assignmentId, tenantId, BigInt(userId));
      },
      { userId, tenantId }
    );
  }

  /**
   * Reactivate a payment plan assignment
   */
  async reactivateAssignment(
    assignmentId: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<void>> {
    return this.executeOperation(
      'reactivateAssignment',
      async () => {
        if (!userId || !tenantId) {
          throw new Error('Missing user or tenant context');
        }

        await reactivatePaymentPlanAssignment(assignmentId, tenantId, BigInt(userId));
      },
      { userId, tenantId }
    );
  }

  /**
   * Delete a payment plan assignment
   */
  async deleteAssignment(
    assignmentId: string,
    userId?: string,
    tenantId?: string,
    preservePaymentStatus: boolean = false
  ): Promise<ServiceResult<void>> {
    return this.executeOperation(
      'deleteAssignment',
      async () => {
        if (!userId || !tenantId) {
          throw new Error('Missing user or tenant context');
        }

        await deletePaymentPlanAssignment(assignmentId, tenantId, BigInt(userId), preservePaymentStatus);
      },
      { userId, tenantId }
    );
  }

  /**
   * Create scheduled payments for active assignments
   */
  async runScheduledPaymentCreation(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'runScheduledPaymentCreation',
      async () => {
        if (!userId || !tenantId) {
          throw new Error('Missing user or tenant context');
        }

        return await createScheduledPayments(tenantId, BigInt(userId));
      },
      { userId, tenantId }
    );
  }

  /**
   * Update overdue payments
   */
  async runOverduePaymentCheck(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'runOverduePaymentCheck',
      async () => {
        if (!userId || !tenantId) {
          throw new Error('Missing user or tenant context');
        }

        return await updateOverduePayments(tenantId, BigInt(userId));
      },
      { userId, tenantId }
    );
  }
}
