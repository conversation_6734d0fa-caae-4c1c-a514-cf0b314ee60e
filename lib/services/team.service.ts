import { BaseService } from './base';
import { ServiceResult, ValidationError } from '../errors/types';
import { NotFoundError, BusinessRuleError } from '../errors/errors';
import { TenantAwareDB } from '../db';
import { getServerTenantId, getServerUserId } from '../tenant-utils-server';
import { db } from '@/src/db';
import { athletes } from '@/src/db/schema';
import { and, eq, inArray } from 'drizzle-orm';
import { getAthletePaymentPlans, deactivatePaymentPlanAssignment, assignPaymentPlanToAthlete, reactivatePaymentPlanAssignment } from '../payment-plan-utils';
import { addAthleteToTeam, getAthleteTeams } from '../actions/athlete-teams';

export interface CreateTeamData {
  name: string;
  schoolId: string;
  branchId: string;
  instructorId: string;
  description?: string;
}

export interface UpdateTeamData {
  name?: string;
  schoolId?: string;
  branchId?: string;
  instructorId?: string;
  description?: string;
}

export interface PaymentPlanAssignmentData {
  teamId: string;
  planId: string;
  athleteIds: string[];
  force?: boolean;
}

export class TeamService extends BaseService {
  constructor() {
    super('TeamService');
  }

  /**
   * Get all teams
   */
  async getTeams(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getTeams',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getTeams(effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'teams',
      }
    );
  }

  /**
   * Get teams with pagination
   */
  async getTeamsPaginated(
    page: number = 1,
    limit: number = 10,
    search?: string,
    sortBy?: string,
    sortOrder?: 'asc' | 'desc',
    filters?: {
      name?: string;
      description?: string;
      schoolName?: string;
      branchName?: string;
      instructorName?: string;
    },
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'getTeamsPaginated',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getTeamsPaginated(effectiveTenantId || undefined, {
          page,
          limit,
          search,
          sortBy,
          sortOrder,
          filters,
        });
      },
      {
        userId,
        tenantId,
        resource: 'teams',
        metadata: { page, limit, search, sortBy, sortOrder, filters },
      }
    );
  }

  /**
   * Get team by ID
   */
  async getTeamById(
    id: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.checkResourceExists(
      'getTeamById',
      'Team',
      id,
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getTeamById(id, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'team',
      }
    );
  }

  /**
   * Create a team with training schedules
   */
  async createTeamWithSchedules(
    teamData: CreateTeamData,
    schedules: any[],
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'createTeamWithSchedules',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.createTeamWithSchedules(teamData, schedules, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'team',
        metadata: { operation: 'createWithSchedules' }
      }
    );
  }

  /**
   * Update a team with training schedules
   */
  async updateTeamWithSchedules(
    teamId: string,
    teamData: UpdateTeamData,
    schedules: any[],
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'updateTeamWithSchedules',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.updateTeamWithSchedules(teamId, teamData, schedules, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'team',
        metadata: { operation: 'updateWithSchedules', teamId }
      }
    );
  }

  /**
   * Delete a team
   */
  async deleteTeam(
    id: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation(
      'deleteTeam',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Check if team exists
        const existingTeam = await TenantAwareDB.getTeamById(id, effectiveTenantId || undefined);
        if (!existingTeam) {
          throw new NotFoundError('Team not found');
        }

        // All related data will be handled by database cascade rules:
        // - Training schedules: CASCADE DELETE (automatically deleted)
        // - Athlete-team relationships: CASCADE DELETE (automatically deleted)
        // - Payment plan assignments: SET NULL on team reference (preserved but team ref removed) 
        // TODO: this might need to be changed to CASCADE if the team deleted the athlete payment plan assignment should also be deleted for that team 

        await TenantAwareDB.deleteTeam(id, effectiveTenantId || undefined);
        return true;
      },
      {
        userId,
        tenantId,
        resource: 'team',
      }
    );
  }

  /**
   * Check for payment plan conflicts in bulk assignment
   */
  async checkPaymentPlanConflicts(
    teamId: string,
    planId: string,
    athleteIds: string[],
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'checkPaymentPlanConflicts',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        if (!effectiveTenantId) {
          throw new Error('Missing tenant context');
        }
        
        // Get all payment plans to find the selected plan name
        const allPlans = await TenantAwareDB.getPaymentPlans(effectiveTenantId);
        const selectedPlan = (allPlans || []).find(p => p.id === planId);

        if (!selectedPlan) {
          throw new Error("Payment plan not found");
        }

        // Get athlete information including status
        const athletesData = await db
          .select({
            id: athletes.id,
            name: athletes.name,
            surname: athletes.surname,
            status: athletes.status
          })
          .from(athletes)
          .where(
            and(
              eq(athletes.tenantId, effectiveTenantId),
              inArray(athletes.id, athleteIds)
            )
          );

        const conflicts = [];
        const inactiveAthletes = [];

        // Check each athlete for existing active payment plans for this team
        for (const athlete of athletesData) {
          try {
            // Check if athlete is inactive
            if (athlete.status === 'inactive') {
              inactiveAthletes.push({
                athleteId: athlete.id,
                athleteName: athlete.name,
                athleteSurname: athlete.surname,
                status: athlete.status || 'active' // Default to 'active' if status is null/undefined
              });
            }

            const athleteAssignments = await getAthletePaymentPlans(athlete.id, effectiveTenantId);
            
            // Find active assignments for this specific team
            const activeTeamAssignment = athleteAssignments.find(
              assignment => 
                assignment.teamId === teamId && 
                assignment.isActive && 
                assignment.planId !== planId // Different plan
            );

            if (activeTeamAssignment) {
              conflicts.push({
                athleteId: athlete.id,
                athleteName: athlete.name,
                athleteSurname: athlete.surname,
                currentPlanName: activeTeamAssignment.planName,
                currentPlanId: activeTeamAssignment.planId,
                athleteStatus: athlete.status || 'active' // Default to 'active' if status is null/undefined
              });
            }
          } catch (error) {
            // Continue with other athletes
          }
        }

        return {
          conflicts,
          inactiveAthletes,
          selectedPlanName: selectedPlan.name
        };
      },
      {
        userId,
        tenantId,
        resource: 'team',
        metadata: { operation: 'checkPaymentPlanConflicts', teamId, planId }
      }
    );
  }

  /**
   * Bulk assign payment plans to athletes in a team
   */
  async bulkAssignPaymentPlan(
    data: PaymentPlanAssignmentData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'bulkAssignPaymentPlan',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        const effectiveUserId = userId || await getServerUserId();
        
        if (!effectiveTenantId) {
          throw new Error('Missing tenant context');
        }
        
        if (!effectiveUserId) {
          throw new Error('Missing user context');
        }
        
        // Check payment plan exists
        const paymentPlan = await TenantAwareDB.getPaymentPlanById(data.planId, effectiveTenantId);
        if (!paymentPlan) {
          throw new NotFoundError('PaymentPlan', data.planId);
        }
        
        // Check team exists
        const team = await TenantAwareDB.getTeamById(data.teamId, effectiveTenantId);
        if (!team) {
          throw new NotFoundError('Team', data.teamId);
        }
        
        // Check for conflicts unless force is true
        if (!data.force) {
          const conflictsResult = await this.checkPaymentPlanConflicts(
            data.teamId,
            data.planId, 
            data.athleteIds,
            userId,
            tenantId
          );
          
          if (conflictsResult.success && conflictsResult.data.conflicts.length > 0) {
            throw new BusinessRuleError(
              'payment_plan_conflicts',
              'Some athletes already have active payment plans for this team',
              undefined,
              'Some athletes already have active payment plans. Use force=true to override.'
            );
          }
        }
        
        // Get athlete information including status
        const athletesData = await db
          .select({
            id: athletes.id,
            status: athletes.status
          })
          .from(athletes)
          .where(
            and(
              eq(athletes.tenantId, effectiveTenantId),
              inArray(athletes.id, data.athleteIds)
            )
          );

        const athleteStatusMap = new Map(athletesData.map(a => [a.id, a.status]));

        let assignedCount = 0;
        let updatedCount = 0;
        const errors = [];
        
        // Process each athlete
        for (const athleteId of data.athleteIds) {
          try {
            const athleteStatus = athleteStatusMap.get(athleteId);
            if (!athleteStatus) {
              errors.push(`Athlete ${athleteId} not found`);
              continue;
            }

            // Determine if the assignment should be active based on athlete status
            const assignmentShouldBeActive = athleteStatus === 'active';

            // Ensure athlete is in the team
            const athleteTeams = await getAthleteTeams(athleteId);
            const isInTeam = athleteTeams.some((team: any) => team.teamId === data.teamId);
            
            if (!isInTeam) {
              await addAthleteToTeam(athleteId, data.teamId);
            }

            // Get current assignments for this athlete
            const athleteAssignments = await getAthletePaymentPlans(athleteId, effectiveTenantId);
            
            // Find active assignments for this specific team
            const activeTeamAssignment = athleteAssignments.find(
              assignment => 
                assignment.teamId === data.teamId && 
                assignment.isActive
            );

            // Check if athlete already has the same plan active for this team
            if (activeTeamAssignment && activeTeamAssignment.planId === data.planId) {
              // Already has the same plan active, skip
              continue;
            }

            // If there's an active assignment for this team with a different plan
            if (activeTeamAssignment && activeTeamAssignment.planId !== data.planId) {
              if (!data.force) {
                // Should not happen if conflicts were checked properly
                errors.push(`Athlete ${athleteId} has conflicting assignment`);
                continue;
              }
              
              // Deactivate the existing assignment
              await deactivatePaymentPlanAssignment(
                activeTeamAssignment.id, 
                effectiveTenantId, 
                BigInt(effectiveUserId.toString())
              );
              updatedCount++;
            }

            // Check if athlete has an inactive assignment for this team and plan
            const inactiveMatchingAssignment = athleteAssignments.find(
              assignment => 
                assignment.teamId === data.teamId && 
                assignment.planId === data.planId &&
                !assignment.isActive
            );

            if (inactiveMatchingAssignment) {
              // If athlete is active, reactivate the assignment; otherwise keep it inactive
              if (assignmentShouldBeActive) {
                await reactivatePaymentPlanAssignment(
                  inactiveMatchingAssignment.id,
                  effectiveTenantId,
                  BigInt(effectiveUserId.toString())
                );
              }
              assignedCount++;
            } else {
              // Create new assignment with appropriate active status
              await assignPaymentPlanToAthlete({
                athleteId,
                planId: data.planId,
                teamId: data.teamId,
                isActive: assignmentShouldBeActive,
                tenantId: effectiveTenantId,
                userId: BigInt(effectiveUserId.toString())
              });
              assignedCount++;
            }
          } catch (error) {
            errors.push(`Failed to process athlete ${athleteId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        }
        
        return {
          success: true,
          assignedCount,
          updatedCount,
          totalProcessed: data.athleteIds.length,
          errors: errors.length > 0 ? errors : undefined
        };
      },
      {
        userId,
        tenantId,
        resource: 'team',
        metadata: { operation: 'bulkAssignPaymentPlan', teamId: data.teamId, planId: data.planId }
      }
    );
  }
}

// Factory function
export const teamService = () => new TeamService();
