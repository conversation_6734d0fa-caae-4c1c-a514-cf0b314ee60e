# SMS Service

The SMS service provides a flexible, adapter-pattern-based solution for sending SMS messages through different providers.

## Architecture

The SMS service uses the adapter pattern to allow easy switching between different SMS providers:

- **SmsService**: Main service class that handles business logic, validation, and error handling
- **SmsProvider Interface**: Contract that all SMS providers must implement
- **Provider Implementations**: Specific implementations for different SMS services (focused only on API integration)

### Separation of Concerns

- **Service Layer**: Handles validation, business rules, error handling, and logging
- **Provider Layer**: Focuses solely on SMS API integration and communication
- **No Duplicate Validation**: Validation happens only at the service layer, providers assume valid input

## Current Providers

### NetGSM Provider
- **File**: `providers/netgsm.provider.ts`
- **Status**: Fully functional using official `@netgsm/sms` SDK
- **Configuration**: Uses environment variables `NETGSM_USERNAME`, `NETGSM_PASSWORD`, `NETGSM_APPNAME`
- **Features**: Real NetGSM API integration with comprehensive error handling

### Mock Provider
- **File**: `providers/mock.provider.ts`
- **Status**: Fully functional for testing
- **Use Case**: Development and testing

## Usage

### Basic Usage

```typescript
import { smsService } from '@/lib/services';

// Send SMS using the default provider
const result = await smsService().sendSms({
  senderIdentifier: 'MYCOMPANY',
  encoding: 'UTF-8',
  messages: [
    {
      receiver: '+**********',
      message: 'Hello, this is a test message!'
    },
    {
      receiver: '+**********',
      message: 'Another message to a different number'
    }
  ]
});

if (result.success) {
  console.log('SMS sent successfully:', result.data);
} else {
  console.error('SMS failed:', result.error);
}
```

### Using Different Providers

To switch to a different provider, you only need to change the import in `sms.service.ts`:

```typescript
// Current (NetGSM)
import { NetGsmProvider } from './sms/providers/netgsm.provider';

// To switch to Mock provider
import { MockSmsProvider } from './sms/providers/mock.provider';

// Then update the constructor
this.provider = config?.provider || new MockSmsProvider();
```

### Custom Provider Configuration

```typescript
import { SmsService } from '@/lib/services';
import { MockSmsProvider } from '@/lib/services/sms';

// Create service with custom provider
const customSmsService = new SmsService({
  provider: new MockSmsProvider({ shouldFail: false, delay: 500 }),
  defaultSender: 'MYAPP',
  defaultEncoding: 'UTF-8',
  enabled: true
});
```

## Environment Variables

```env
# NetGSM Configuration
NETGSM_USERNAME=your_username
NETGSM_PASSWORD=your_password
NETGSM_APPNAME=your_app_name  # Optional

# SMS Service Configuration
SMS_DEFAULT_SENDER=YOURCOMPANY
SMS_DEFAULT_ENCODING=UTF-8
SMS_ENABLED=true
```

## Adding New Providers

To add a new SMS provider:

1. Create a new file in `providers/` directory
2. Implement the `SmsProvider` interface
3. Update the import in `sms.service.ts`

Example:

```typescript
// providers/twilio.provider.ts
import { SmsProvider, SendSmsParams, SmsProviderResponse } from '../types';

export class TwilioProvider implements SmsProvider {
  async sendSms(params: SendSmsParams): Promise<SmsProviderResponse> {
    // Implement Twilio API integration
  }
  
  getProviderName(): string {
    return 'Twilio';
  }
}
```

## Error Handling

The service includes comprehensive error handling:

- **Validation Errors**: Invalid parameters, missing required fields
- **Business Rule Errors**: Service disabled, provider configuration issues
- **Provider Errors**: SMS provider-specific errors

## Service Status

Check service configuration and status:

```typescript
const status = await smsService().getServiceStatus();
console.log('SMS Service Status:', status.data);
```

## Testing

Use the Mock provider for testing:

```typescript
import { SmsService } from '@/lib/services';
import { MockSmsProvider } from '@/lib/services/sms';

const testService = new SmsService({
  provider: new MockSmsProvider({ shouldFail: false })
});
```
