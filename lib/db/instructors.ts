import { eq, and, like, ilike, count, desc, asc, or, inArray } from 'drizzle-orm';
import { TenantAwareDBBase } from './base';
import * as schema from '@/src/db/schema';

/**
 * Database operations for Instructors
 */
export class InstructorsDB extends TenantAwareDBBase {
  static async getInstructors(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    // Get instructors with their branches and schools
    const instructorsWithRelations = await this.database
      .select({
        id: schema.instructors.id,
        name: schema.instructors.name,
        surname: schema.instructors.surname,
        email: schema.instructors.email,
        phone: schema.instructors.phone,
        nationalId: schema.instructors.nationalId,
        birthDate: schema.instructors.birthDate,
        address: schema.instructors.address,
        salary: schema.instructors.salary,
        createdAt: schema.instructors.createdAt,
        updatedAt: schema.instructors.updatedAt,
        createdBy: schema.instructors.createdBy,
        updatedBy: schema.instructors.updatedBy,
        // Get branch info if related
        branchId: schema.branches.id,
        branchName: schema.branches.name,
        // Get school info if related
        schoolId: schema.schools.id,
      })
      .from(schema.instructors)
      .leftJoin(
        schema.instructorBranches,
        eq(schema.instructorBranches.instructorId, schema.instructors.id)
      )
      .leftJoin(
        schema.branches,
        eq(schema.branches.id, schema.instructorBranches.branchId)
      )
      .leftJoin(
        schema.instructorSchools,
        eq(schema.instructorSchools.instructorId, schema.instructors.id)
      )
      .leftJoin(
        schema.schools,
        eq(schema.schools.id, schema.instructorSchools.schoolId)
      )
      .where(eq(schema.instructors.tenantId, filter.tenantId));

    // Group results to construct the expected format
    const instructorMap = new Map();
    
    for (const row of instructorsWithRelations) {
      if (!instructorMap.has(row.id)) {
        instructorMap.set(row.id, {
          id: row.id,
          name: row.name,
          surname: row.surname,
          email: row.email,
          phone: row.phone,
          nationalId: row.nationalId,
          birthDate: row.birthDate,
          address: row.address,
          salary: row.salary,
          createdAt: row.createdAt,
          updatedAt: row.updatedAt,
          createdBy: row.createdBy,
          updatedBy: row.updatedBy,
          branches: [],
          schools: [],
        });
      }
      
      const instructor = instructorMap.get(row.id);
      
      // Add branch if exists and not already added
      if (row.branchId && row.branchName && !instructor.branches.find((b: any) => b.id === row.branchId)) {
        instructor.branches.push({
          id: row.branchId,
          name: row.branchName,
        });
      }
      
      // Add school ID if exists and not already added
      if (row.schoolId && !instructor.schools.includes(row.schoolId)) {
        instructor.schools.push(row.schoolId);
      }
    }
    
    return Array.from(instructorMap.values());
  }

  static async getInstructorsPaginated(
    tenantId?: string,
    options: {
      page: number;
      limit: number;
      search?: string;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      filters?: {
        name?: string;
        surname?: string;
        email?: string;
        phone?: string;
        branchId?: string;
        schoolId?: string;
      };
    } = { page: 1, limit: 10 }
  ) {
    const filter = await this.getTenantFilter(tenantId);
    const { page, limit, search, sortBy = 'createdAt', sortOrder = 'desc', filters = {} } = options;
    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [eq(schema.instructors.tenantId, filter.tenantId)];

    // Add search condition
    if (search) {
      whereConditions.push(
        or(
          ilike(schema.instructors.name, `%${search}%`),
          ilike(schema.instructors.surname, `%${search}%`),
          ilike(schema.instructors.email, `%${search}%`),
          ilike(schema.instructors.phone, `%${search}%`)
        )!
      );
    }

    // Add column-based filters
    if (filters.name) {
      whereConditions.push(ilike(schema.instructors.name, `%${filters.name}%`));
    }
    if (filters.surname) {
      whereConditions.push(ilike(schema.instructors.surname, `%${filters.surname}%`));
    }
    if (filters.email) {
      whereConditions.push(ilike(schema.instructors.email, `%${filters.email}%`));
    }
    if (filters.phone) {
      whereConditions.push(ilike(schema.instructors.phone, `%${filters.phone}%`));
    }

    const whereClause = whereConditions.length > 1 ? and(...whereConditions) : whereConditions[0];

    // Get total count
    const totalResult = await this.database
      .select({ count: count() })
      .from(schema.instructors)
      .where(whereClause);

    const total = totalResult[0]?.count || 0;

    // Determine sort column and order
    let orderByClause;
    switch (sortBy) {
      case 'name':
        orderByClause = sortOrder === 'asc' ? asc(schema.instructors.name) : desc(schema.instructors.name);
        break;
      case 'surname':
        orderByClause = sortOrder === 'asc' ? asc(schema.instructors.surname) : desc(schema.instructors.surname);
        break;
      case 'email':
        orderByClause = sortOrder === 'asc' ? asc(schema.instructors.email) : desc(schema.instructors.email);
        break;
      case 'phone':
        orderByClause = sortOrder === 'asc' ? asc(schema.instructors.phone) : desc(schema.instructors.phone);
        break;
      case 'createdAt':
      default:
        orderByClause = sortOrder === 'asc' ? asc(schema.instructors.createdAt) : desc(schema.instructors.createdAt);
        break;
    }

    // First, get the paginated instructor IDs
    const instructorIds = await this.database
      .select({ id: schema.instructors.id })
      .from(schema.instructors)
      .where(whereClause)
      .orderBy(orderByClause)
      .limit(limit)
      .offset(offset);

    if (instructorIds.length === 0) {
      return {
        data: [],
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNextPage: page < Math.ceil(total / limit),
          hasPreviousPage: page > 1,
        },
      };
    }

    // Then get the full instructor data with relationships for the paginated IDs
    const instructorsWithRelations = await this.database
      .select({
        id: schema.instructors.id,
        name: schema.instructors.name,
        surname: schema.instructors.surname,
        email: schema.instructors.email,
        phone: schema.instructors.phone,
        nationalId: schema.instructors.nationalId,
        birthDate: schema.instructors.birthDate,
        address: schema.instructors.address,
        salary: schema.instructors.salary,
        createdAt: schema.instructors.createdAt,
        updatedAt: schema.instructors.updatedAt,
        createdBy: schema.instructors.createdBy,
        updatedBy: schema.instructors.updatedBy,
        // Get branch info if related
        branchId: schema.branches.id,
        branchName: schema.branches.name,
        // Get school info if related
        schoolId: schema.schools.id,
      })
      .from(schema.instructors)
      .leftJoin(
        schema.instructorBranches,
        eq(schema.instructorBranches.instructorId, schema.instructors.id)
      )
      .leftJoin(
        schema.branches,
        eq(schema.branches.id, schema.instructorBranches.branchId)
      )
      .leftJoin(
        schema.instructorSchools,
        eq(schema.instructorSchools.instructorId, schema.instructors.id)
      )
      .leftJoin(
        schema.schools,
        eq(schema.schools.id, schema.instructorSchools.schoolId)
      )
      .where(
        inArray(schema.instructors.id, instructorIds.map(i => i.id))
      )
      .orderBy(orderByClause);

    // Group results to construct the expected format
    const instructorMap = new Map();
    
    for (const row of instructorsWithRelations) {
      if (!instructorMap.has(row.id)) {
        instructorMap.set(row.id, {
          id: row.id,
          name: row.name,
          surname: row.surname,
          email: row.email,
          phone: row.phone,
          nationalId: row.nationalId,
          birthDate: row.birthDate,
          address: row.address,
          salary: row.salary,
          createdAt: row.createdAt,
          updatedAt: row.updatedAt,
          createdBy: row.createdBy,
          updatedBy: row.updatedBy,
          branches: [],
          schools: [],
        });
      }
      
      const instructor = instructorMap.get(row.id);
      
      // Add branch if exists and not already added
      if (row.branchId && row.branchName && !instructor.branches.find((b: any) => b.id === row.branchId)) {
        instructor.branches.push({
          id: row.branchId,
          name: row.branchName,
        });
      }
      
      // Add school ID if exists and not already added
      if (row.schoolId && !instructor.schools.includes(row.schoolId)) {
        instructor.schools.push(row.schoolId);
      }
    }

    const instructors = Array.from(instructorMap.values());

    return {
      data: instructors,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPreviousPage: page > 1,
      },
    };
  }

  static async getInstructorById(id: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    // Get instructor with their branches and schools
    const instructorWithRelations = await this.database
      .select({
        id: schema.instructors.id,
        name: schema.instructors.name,
        surname: schema.instructors.surname,
        email: schema.instructors.email,
        phone: schema.instructors.phone,
        nationalId: schema.instructors.nationalId,
        birthDate: schema.instructors.birthDate,
        address: schema.instructors.address,
        salary: schema.instructors.salary,
        createdAt: schema.instructors.createdAt,
        updatedAt: schema.instructors.updatedAt,
        createdBy: schema.instructors.createdBy,
        updatedBy: schema.instructors.updatedBy,
        // Get branch info if related
        branchId: schema.branches.id,
        branchName: schema.branches.name,
        // Get school info if related
        schoolId: schema.schools.id,
      })
      .from(schema.instructors)
      .leftJoin(
        schema.instructorBranches,
        eq(schema.instructorBranches.instructorId, schema.instructors.id)
      )
      .leftJoin(
        schema.branches,
        eq(schema.branches.id, schema.instructorBranches.branchId)
      )
      .leftJoin(
        schema.instructorSchools,
        eq(schema.instructorSchools.instructorId, schema.instructors.id)
      )
      .leftJoin(
        schema.schools,
        eq(schema.schools.id, schema.instructorSchools.schoolId)
      )
      .where(and(
        eq(schema.instructors.id, id),
        eq(schema.instructors.tenantId, filter.tenantId)
      ));

    if (instructorWithRelations.length === 0) {
      return null;
    }

    // Build the instructor object from the joined results
    const firstRow = instructorWithRelations[0];
    const instructor = {
      id: firstRow.id,
      name: firstRow.name,
      surname: firstRow.surname,
      email: firstRow.email,
      phone: firstRow.phone,
      nationalId: firstRow.nationalId,
      birthDate: firstRow.birthDate,
      address: firstRow.address,
      salary: firstRow.salary,
      createdAt: firstRow.createdAt,
      updatedAt: firstRow.updatedAt,
      createdBy: firstRow.createdBy,
      updatedBy: firstRow.updatedBy,
      branches: [] as Array<{ id: string; name: string }>,
      schools: [] as string[],
    };

    // Collect unique branches and schools
    for (const row of instructorWithRelations) {
      // Add branch if exists and not already added
      if (row.branchId && row.branchName && !instructor.branches.find(b => b.id === row.branchId)) {
        instructor.branches.push({
          id: row.branchId,
          name: row.branchName,
        });
      }
      
      // Add school ID if exists and not already added
      if (row.schoolId && !instructor.schools.includes(row.schoolId)) {
        instructor.schools.push(row.schoolId);
      }
    }
    
    return instructor;
  }

  static async getInstructorByEmail(email: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    const instructor = await this.database
      .select()
      .from(schema.instructors)
      .where(and(
        eq(schema.instructors.email, email),
        eq(schema.instructors.tenantId, filter.tenantId)
      ))
      .limit(1);

    return instructor[0] || null;
  }

  static async getInstructorByNationalId(nationalId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    const instructor = await this.database
      .select()
      .from(schema.instructors)
      .where(and(
        eq(schema.instructors.nationalId, nationalId),
        eq(schema.instructors.tenantId, filter.tenantId)
      ))
      .limit(1);

    return instructor[0] || null;
  }

  static async getTeamsByInstructorId(instructorId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    const teams = await this.database
      .select()
      .from(schema.teams)
      .where(and(
        eq(schema.teams.instructorId, instructorId),
        eq(schema.teams.tenantId, filter.tenantId)
      ));

    return teams;
  }

  // Instructor-Branch relationships
  static async updateInstructorBranches(instructorId: string, branchIds: string[], tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    // Start a transaction to ensure consistency
    return await this.database.transaction(async (tx) => {
      // First, remove all existing instructor-branch relationships
      await tx.delete(schema.instructorBranches)
        .where(eq(schema.instructorBranches.instructorId, instructorId));
      
      // Then, add the new relationships
      if (branchIds.length > 0) {
        const instructorBranchData = branchIds.map(branchId => ({
          instructorId,
          branchId,
          tenantId: filter.tenantId
        }));
        
        await tx.insert(schema.instructorBranches)
          .values(instructorBranchData);
      }
      
      return true;
    });
  }

  static async updateInstructorSchools(instructorId: string, schoolIds: string[], tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    // Start a transaction to ensure consistency
    return await this.database.transaction(async (tx) => {
      // First, remove all existing instructor-school relationships
      await tx.delete(schema.instructorSchools)
        .where(eq(schema.instructorSchools.instructorId, instructorId));
      
      // Then, add the new relationships
      if (schoolIds.length > 0) {
        const instructorSchoolData = schoolIds.map(schoolId => ({
          instructorId,
          schoolId
        }));
        
        await tx.insert(schema.instructorSchools)
          .values(instructorSchoolData);
      }
      
      return true;
    });
  }

  static async createInstructor(data: Omit<typeof schema.instructors.$inferInsert, 'tenantId' | 'createdBy' | 'updatedBy' | 'createdAt' | 'updatedAt'>, tenantId?: string, userId?: bigint) {
    return this.insertWithAudit(schema.instructors, data, tenantId, userId);
  }

  static async updateInstructor(id: string, data: Partial<typeof schema.instructors.$inferInsert>, tenantId?: string, userId?: bigint) {
    return this.updateWithAudit(schema.instructors, id, data, tenantId, userId);
  }

  static async deleteInstructor(id: string, tenantId?: string) {
    return this.deleteWithTenantFilter(schema.instructors, id, tenantId);
  }
}
