import { eq, and, desc, gte, lte, like, count, sql } from 'drizzle-orm';
import { db } from '@/src/db';
import * as schema from '@/src/db/schema';
import { TenantAwareDBBase } from './base';

export class SmsLogsDB extends TenantAwareDBBase {
  
  static async getSmsLogs(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select({
      id: schema.smsLogs.id,
      type: schema.smsLogs.type,
      status: schema.smsLogs.status,
      receiver: schema.smsLogs.receiver,
      message: schema.smsLogs.message,
      senderIdentifier: schema.smsLogs.senderIdentifier,
      sentAt: schema.smsLogs.sentAt,
      creditsUsed: schema.smsLogs.creditsUsed,
      senderType: schema.smsLogs.senderType,
      senderId: schema.smsLogs.senderId,
      createdAt: schema.smsLogs.createdAt,
      athlete: {
        id: schema.athletes.id,
        name: schema.athletes.name,
        surname: schema.athletes.surname,
      },

      team: {
        id: schema.teams.id,
        name: schema.teams.name,
      }
    })
    .from(schema.smsLogs)
    .leftJoin(schema.athletes, eq(schema.smsLogs.athleteId, schema.athletes.id))
    .leftJoin(schema.teams, eq(schema.smsLogs.teamId, schema.teams.id))
    .where(eq(schema.smsLogs.tenantId, filter.tenantId))
    .orderBy(desc(schema.smsLogs.sentAt));
  }

  static async getSmsLogsPaginated(
    tenantId?: string,
    options: {
      page: number;
      limit: number;
      search?: string;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      filters?: {
        type?: string;
        status?: string;
        senderType?: string;
        dateFrom?: string;
        dateTo?: string;
      };
    } = { page: 1, limit: 10 }
  ) {
    const filter = await this.getTenantFilter(tenantId);
    const { page, limit, search, sortBy = 'sentAt', sortOrder = 'desc', filters = {} } = options;
    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [eq(schema.smsLogs.tenantId, filter.tenantId)];

    if (search) {
      whereConditions.push(
        sql`(${schema.smsLogs.receiver} ILIKE ${`%${search}%`} OR ${schema.smsLogs.message} ILIKE ${`%${search}%`})`
      );
    }

    if (filters.type) {
      whereConditions.push(eq(schema.smsLogs.type, filters.type as any));
    }

    if (filters.status) {
      whereConditions.push(eq(schema.smsLogs.status, filters.status as any));
    }

    if (filters.senderType) {
      whereConditions.push(eq(schema.smsLogs.senderType, filters.senderType));
    }

    if (filters.dateFrom) {
      whereConditions.push(gte(schema.smsLogs.sentAt, new Date(filters.dateFrom)));
    }

    if (filters.dateTo) {
      whereConditions.push(lte(schema.smsLogs.sentAt, new Date(filters.dateTo)));
    }

    // Get total count
    const totalResult = await db.select({ count: count() })
      .from(schema.smsLogs)
      .where(and(...whereConditions));
    const total = totalResult[0]?.count || 0;

    // Get paginated results
    const orderByClause = sortOrder === 'asc' ? schema.smsLogs.sentAt : desc(schema.smsLogs.sentAt);

    const result = await db.select({
      id: schema.smsLogs.id,
      type: schema.smsLogs.type,
      status: schema.smsLogs.status,
      receiver: schema.smsLogs.receiver,
      message: schema.smsLogs.message,
      senderIdentifier: schema.smsLogs.senderIdentifier,
      sentAt: schema.smsLogs.sentAt,
      creditsUsed: schema.smsLogs.creditsUsed,
      senderType: schema.smsLogs.senderType,
      senderId: schema.smsLogs.senderId,
      createdAt: schema.smsLogs.createdAt,
      athlete: {
        id: schema.athletes.id,
        name: schema.athletes.name,
        surname: schema.athletes.surname,
      },
      payment: {
        id: schema.payments.id,
        amount: schema.payments.amount,
        dueDate: schema.payments.dueDate,
      },
      team: {
        id: schema.teams.id,
        name: schema.teams.name,
      }
    })
    .from(schema.smsLogs)
    .leftJoin(schema.athletes, eq(schema.smsLogs.athleteId, schema.athletes.id))
    .leftJoin(schema.teams, eq(schema.smsLogs.teamId, schema.teams.id))
    .where(and(...whereConditions))
    .orderBy(orderByClause)
    .limit(limit)
    .offset(offset);

    return {
      data: result,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPreviousPage: page > 1,
      },
    };
  }

  static async getSmsLogById(id: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select({
      id: schema.smsLogs.id,
      type: schema.smsLogs.type,
      status: schema.smsLogs.status,
      receiver: schema.smsLogs.receiver,
      message: schema.smsLogs.message,
      senderIdentifier: schema.smsLogs.senderIdentifier,
      sentAt: schema.smsLogs.sentAt,
      creditsUsed: schema.smsLogs.creditsUsed,
      providerResponse: schema.smsLogs.providerResponse,
      senderType: schema.smsLogs.senderType,
      senderId: schema.smsLogs.senderId,
      createdAt: schema.smsLogs.createdAt,
      updatedAt: schema.smsLogs.updatedAt,
      athlete: {
        id: schema.athletes.id,
        name: schema.athletes.name,
        surname: schema.athletes.surname,
        parentPhone: schema.athletes.parentPhone,
      },
      payment: {
        id: schema.payments.id,
        amount: schema.payments.amount,
        dueDate: schema.payments.dueDate,
        status: schema.payments.status,
      },
      team: {
        id: schema.teams.id,
        name: schema.teams.name,
      }
    })
    .from(schema.smsLogs)
    .leftJoin(schema.athletes, eq(schema.smsLogs.athleteId, schema.athletes.id))
    .leftJoin(schema.teams, eq(schema.smsLogs.teamId, schema.teams.id))
    .where(and(
      eq(schema.smsLogs.id, id),
      eq(schema.smsLogs.tenantId, filter.tenantId)
    ));
    
    return result[0] || null;
  }

  static async createSmsLog(
    data: {
      type: 'payment_reminder' | 'team_message' | 'custom';
      status: 'pending' | 'sent' | 'failed' | 'cancelled';
      receiver: string;
      message: string;
      senderIdentifier: string;
      creditsUsed?: number;
      providerResponse?: string;
      athleteId?: string;
      teamId?: string;
      senderType: 'user' | 'system';
      senderId?: string;
    },
    tenantId?: string
  ) {
    const filter = await this.getTenantFilter(tenantId);
    
    const insertData = {
      ...data,
      ...filter,
      sentAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const result = await db.insert(schema.smsLogs)
      .values(insertData)
      .returning();
    
    return result[0];
  }

  static async updateSmsLog(
    id: string,
    data: {
      status?: 'pending' | 'sent' | 'failed' | 'cancelled';
      creditsUsed?: number;
      providerResponse?: string;
    },
    tenantId?: string
  ) {
    const filter = await this.getTenantFilter(tenantId);
    
    const result = await db.update(schema.smsLogs)
      .set({
        ...data,
        updatedAt: new Date(),
      })
      .where(and(
        eq(schema.smsLogs.id, id),
        eq(schema.smsLogs.tenantId, filter.tenantId)
      ))
      .returning();
    
    return result[0];
  }



  static async getSmsLogsByAthleteId(athleteId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select().from(schema.smsLogs)
      .where(and(
        eq(schema.smsLogs.athleteId, athleteId),
        eq(schema.smsLogs.tenantId, filter.tenantId)
      ))
      .orderBy(desc(schema.smsLogs.sentAt));
  }

  static async getSmsLogsByTeamId(teamId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select().from(schema.smsLogs)
      .where(and(
        eq(schema.smsLogs.teamId, teamId),
        eq(schema.smsLogs.tenantId, filter.tenantId)
      ))
      .orderBy(desc(schema.smsLogs.sentAt));
  }

  static async getSmsStats(tenantId?: string, dateFrom?: Date, dateTo?: Date) {
    const filter = await this.getTenantFilter(tenantId);
    const whereConditions = [eq(schema.smsLogs.tenantId, filter.tenantId)];

    if (dateFrom) {
      whereConditions.push(gte(schema.smsLogs.sentAt, dateFrom));
    }

    if (dateTo) {
      whereConditions.push(lte(schema.smsLogs.sentAt, dateTo));
    }

    const result = await db.select({
      total: count(),
      type: schema.smsLogs.type,
      status: schema.smsLogs.status,
    })
    .from(schema.smsLogs)
    .where(and(...whereConditions))
    .groupBy(schema.smsLogs.type, schema.smsLogs.status);

    return result;
  }

  static async createSmsLogPaymentRelation(
    smsLogId: string,
    paymentId: string,
    tenantId?: string
  ) {
    const result = await db.insert(schema.smsLogPayments)
      .values({
        smsLogId,
        paymentId,
      })
      .returning();

    return result[0];
  }
}
