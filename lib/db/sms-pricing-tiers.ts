import { db } from '@/src/db';
import * as schema from '@/src/db/schema';
import { TenantAwareDBBase } from './base';
import { eq, and, asc } from 'drizzle-orm';

export class SmsPricingTiersDB extends TenantAwareDBBase {
  
  /**
   * Get all active SMS pricing tiers
   */
  static async getActivePricingTiers() {
    return db.select().from(schema.smsPricingTiers)
      .where(eq(schema.smsPricingTiers.isActive, true))
      .orderBy(asc(schema.smsPricingTiers.sortOrder), asc(schema.smsPricingTiers.minCredits));
  }

  /**
   * Get all SMS pricing tiers (including inactive)
   */
  static async getAllPricingTiers() {
    return db.select().from(schema.smsPricingTiers)
      .orderBy(asc(schema.smsPricingTiers.sortOrder), asc(schema.smsPricingTiers.minCredits));
  }

  /**
   * Get pricing tier by ID
   */
  static async getPricingTierById(id: string) {
    const result = await db.select().from(schema.smsPricingTiers)
      .where(eq(schema.smsPricingTiers.id, id))
      .limit(1);
    
    return result[0] || null;
  }

  /**
   * Create a new pricing tier
   */
  static async createPricingTier(
    data: {
      name: string;
      description: string;
      minCredits: number;
      maxCredits?: number | null;
      pricePerCredit: number;
      currency?: string;
      sortOrder?: number;
    },
    userId?: bigint
  ) {
    const auditInfo = await this.getAuditInfo(userId);

    const insertData = {
      ...data,
      ...auditInfo,
      currency: data.currency || 'USD',
      sortOrder: data.sortOrder || 0,
      isActive: true,
    };

    const result = await db.insert(schema.smsPricingTiers)
      .values(insertData)
      .returning();

    return result[0];
  }

  /**
   * Update a pricing tier
   */
  static async updatePricingTier(
    id: string,
    data: {
      name?: string;
      description?: string;
      minCredits?: number;
      maxCredits?: number | null;
      pricePerCredit?: number;
      currency?: string;
      isActive?: boolean;
      sortOrder?: number;
    },
    userId?: bigint
  ) {
    const auditInfo = await this.getAuditInfo(userId);

    const updateData = {
      ...data,
      updatedAt: auditInfo.updatedAt,
      updatedBy: auditInfo.updatedBy,
    };

    const result = await db.update(schema.smsPricingTiers)
      .set(updateData)
      .where(eq(schema.smsPricingTiers.id, id))
      .returning();

    return result[0] || null;
  }

  /**
   * Delete a pricing tier
   */
  static async deletePricingTier(id: string) {
    const result = await db.delete(schema.smsPricingTiers)
      .where(eq(schema.smsPricingTiers.id, id))
      .returning();

    return result[0] || null;
  }

  /**
   * Activate/deactivate a pricing tier
   */
  static async togglePricingTierStatus(id: string, isActive: boolean, userId?: bigint) {
    const auditInfo = await this.getAuditInfo(userId);

    const result = await db.update(schema.smsPricingTiers)
      .set({
        isActive,
        updatedAt: auditInfo.updatedAt,
        updatedBy: auditInfo.updatedBy,
      })
      .where(eq(schema.smsPricingTiers.id, id))
      .returning();

    return result[0] || null;
  }

  /**
   * Initialize default pricing tiers
   */
  static async initializeDefaultPricingTiers(userId?: bigint) {
    // Check if any pricing tiers exist
    const existing = await this.getAllPricingTiers();
    if (existing.length > 0) {
      return existing; // Don't initialize if tiers already exist
    }

    const defaultTiers = [
      {
        name: 'starter',
        description: 'Starter tier - up to 100 credits',
        minCredits: 1,
        maxCredits: 100,
        pricePerCredit: 10, // $0.10
        sortOrder: 1,
      },
      {
        name: 'standard',
        description: 'Standard tier - 101-500 credits',
        minCredits: 101,
        maxCredits: 500,
        pricePerCredit: 8, // $0.08
        sortOrder: 2,
      },
      {
        name: 'professional',
        description: 'Professional tier - 501-1000 credits',
        minCredits: 501,
        maxCredits: 1000,
        pricePerCredit: 6, // $0.06
        sortOrder: 3,
      },
      {
        name: 'enterprise',
        description: 'Enterprise tier - 1000+ credits',
        minCredits: 1001,
        maxCredits: null,
        pricePerCredit: 5, // $0.05
        sortOrder: 4,
      },
    ];

    const createdTiers = [];
    for (const tier of defaultTiers) {
      const created = await this.createPricingTier(tier, userId);
      createdTiers.push(created);
    }

    return createdTiers;
  }
}
