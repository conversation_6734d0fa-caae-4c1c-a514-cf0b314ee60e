import { eq, and } from 'drizzle-orm';
import { db } from '@/src/db';
import * as schema from '@/src/db/schema';
import { TenantAwareDBBase } from './base';

export class FinancialDB extends TenantAwareDBBase {
  
  static async getFinancialSummary(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    // Get all payments and expenses for the tenant
    const completedPayments = await db.select().from(schema.payments)
      .where(and(
        eq(schema.payments.tenantId, filter.tenantId),
        eq(schema.payments.status, 'completed')
      ));
    
    const overduePayments = await db.select().from(schema.payments)
      .where(and(
        eq(schema.payments.tenantId, filter.tenantId),
        eq(schema.payments.status, 'overdue')
      ));
    
    const allExpenses = await db.select().from(schema.expenses)
      .where(eq(schema.expenses.tenantId, filter.tenantId));
    
    // Calculate totals
    const totalIncome = completedPayments.reduce((sum: number, payment: any) => sum + parseFloat(payment.amount), 0);
    const totalOverdue = overduePayments.reduce((sum: number, payment: any) => sum + parseFloat(payment.amount), 0);
    const totalExpenses = allExpenses.reduce((sum: number, expense: any) => sum + parseFloat(expense.amount), 0);
    
    // Generate monthly data (last 3 months)
    const currentDate = new Date();
    const months = [];
    for (let i = 2; i >= 0; i--) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      months.push({
        month: date.toLocaleDateString('en-US', { month: 'long' }),
        date: date
      });
    }
    
    // Calculate income by month
    const incomeByMonth = months.map(({ month, date }) => {
      const nextMonth = new Date(date.getFullYear(), date.getMonth() + 1, 1);
      const monthlyIncome = completedPayments
        .filter((payment: any) => {
          const paymentDate = new Date(payment.createdAt);
          return paymentDate >= date && paymentDate < nextMonth;
        })
        .reduce((sum: number, payment: any) => sum + parseFloat(payment.amount), 0);
      
      return { month, amount: monthlyIncome };
    });
    
    // Calculate expenses by month
    const expensesByMonth = months.map(({ month, date }) => {
      const nextMonth = new Date(date.getFullYear(), date.getMonth() + 1, 1);
      const monthlyExpenses = allExpenses
        .filter((expense: any) => {
          const expenseDate = new Date(expense.createdAt);
          return expenseDate >= date && expenseDate < nextMonth;
        })
        .reduce((sum: number, expense: any) => sum + parseFloat(expense.amount), 0);
      
      return { month, amount: monthlyExpenses };
    });
    
    // Calculate income by category (assuming payment plans have categories)
    const incomeByCategory = [
      { 
        category: 'Monthly Fees', 
        amount: completedPayments
          .filter((payment: any) => payment.description?.toLowerCase().includes('monthly') || payment.description?.toLowerCase().includes('aylık'))
          .reduce((sum: number, payment: any) => sum + parseFloat(payment.amount), 0)
      },
      { 
        category: 'Quarterly Fees', 
        amount: completedPayments
          .filter((payment: any) => payment.description?.toLowerCase().includes('quarterly') || payment.description?.toLowerCase().includes('üç aylık'))
          .reduce((sum: number, payment: any) => sum + parseFloat(payment.amount), 0)
      },
      { 
        category: 'Other Fees', 
        amount: completedPayments
          .filter((payment: any) => !payment.description?.toLowerCase().includes('monthly') && 
                            !payment.description?.toLowerCase().includes('aylık') &&
                            !payment.description?.toLowerCase().includes('quarterly') &&
                            !payment.description?.toLowerCase().includes('üç aylık'))
          .reduce((sum: number, payment: any) => sum + parseFloat(payment.amount), 0)
      }
    ];
    
    // Calculate expenses by category
    const expensesByCategory = allExpenses.reduce((acc: Array<{ category: string; amount: number }>, expense: any) => {
      const category = expense.category;
      const existingCategory = acc.find(item => item.category === category);
      
      if (existingCategory) {
        existingCategory.amount += parseFloat(expense.amount);
      } else {
        acc.push({ category, amount: parseFloat(expense.amount) });
      }
      
      return acc;
    }, [] as Array<{ category: string; amount: number }>);
    
    return {
      totalIncome,
      totalExpenses,
      totalOverdue,
      balance: totalIncome - totalExpenses,
      incomeByMonth,
      expensesByMonth,
      incomeByCategory,
      expensesByCategory,
    };
  }
}
