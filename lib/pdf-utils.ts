import jsPDF from 'jspdf';
import { Team, TrainingSchedule } from './types';

// Turkish character map for fallback
const turkishCharMap: Record<string, string> = {
  'ğ': 'g', 'Ğ': 'G',
  'ü': 'u', 'Ü': 'U',
  'ş': 's', 'Ş': 'S',
  'ı': 'i', 'İ': 'I',
  'ö': 'o', 'Ö': 'O',
  'ç': 'c', 'Ç': 'C'
};

// Function to normalize Turkish text for fallback scenarios
function normalizeTurkishText(text: string): string {
  if (!text) return '';
  
  return text.replace(/[ğĞüÜşŞıİöÖçÇ]/g, match => turkishCharMap[match] || match);
}

interface TrainingScheduleExportOptions {
  team: Team;
  t: (key: string, options?: any) => string;
  landscape?: boolean;
}

export function generateTeamSchedulePDF({ team, t, landscape = true }: TrainingScheduleExportOptions): jsPDF {
  // Create a new PDF document
  const doc = new jsPDF({
    orientation: landscape ? 'landscape' : 'portrait',
    unit: 'mm',
    format: 'a4'
  });
  
  // Set font size and style
  doc.setFontSize(20);
  
  // Add title
  const title = `${normalizeTurkishText(team.name)} - ${normalizeTurkishText(t('teams.details.schedule'))}`;
  doc.text(title, landscape ? 150 : 105, 20, { align: 'center' });
  
  // Add team details
  doc.setFontSize(12);
  doc.text(`${normalizeTurkishText(t('schools.title'))}: ${normalizeTurkishText(team.school?.name || '-')}`, 14, 30);
  doc.text(`${normalizeTurkishText(t('instructors.title'))}: ${normalizeTurkishText(team.instructor?.name || '')} ${normalizeTurkishText(team.instructor?.surname || '')}`, 14, 37);
  doc.text(`${normalizeTurkishText(t('teams.details.branch'))}: ${normalizeTurkishText(team.branch?.name ? t(`common.branches.${team.branch.name}`, { ns: 'shared' }) : '-')}`, 14, 44);
  
  // Prepare to draw the schedule grid
  const startY = 55;
  const cellWidth = landscape ? 40 : 30;
  const cellHeight = 25;
  const daysOfWeek = [
    normalizeTurkishText(t('teams.days.sunday')),
    normalizeTurkishText(t('teams.days.monday')),
    normalizeTurkishText(t('teams.days.tuesday')), 
    normalizeTurkishText(t('teams.days.wednesday')), 
    normalizeTurkishText(t('teams.days.thursday')), 
    normalizeTurkishText(t('teams.days.friday')), 
    normalizeTurkishText(t('teams.days.saturday'))
  ];
  
  // Draw days of week headers
  doc.setFillColor(220, 220, 220); // Light gray background
  for (let i = 0; i < 7; i++) {
    const x = 14 + (i * cellWidth);
    doc.setFillColor(230, 230, 230);
    doc.rect(x, startY, cellWidth, 10, 'F');
    doc.setTextColor(0, 0, 0);
    doc.text(daysOfWeek[i], x + cellWidth / 2, startY + 7, { align: 'center' });
  }
  
  // Draw calendar grid and add training times
  const colors = ['#e3f2fd', '#bbdefb', '#90caf9', '#64b5f6', '#42a5f5', '#2196f3', '#1e88e5'];
  
  if (team.trainingSchedule && team.trainingSchedule.length > 0) {
    // Create a map to organize sessions by day
    const scheduleByDay = new Map();
    
    team.trainingSchedule.forEach(schedule => {
      const dayIndex = schedule.dayOfWeek;
      if (!scheduleByDay.has(dayIndex)) {
        scheduleByDay.set(dayIndex, []);
      }
      scheduleByDay.get(dayIndex).push(schedule);
    });
    
    // Draw schedule cells
    for (let i = 0; i < 7; i++) {
      const x = 14 + (i * cellWidth);
      const y = startY + 10;
      
      // Draw empty cell
      doc.setDrawColor(200, 200, 200);
      doc.rect(x, y, cellWidth, cellHeight);
      
      // If there are training sessions on this day, add them
      if (scheduleByDay.has(i)) {
        const sessions = scheduleByDay.get(i);
        const colorIndex = i % colors.length;
        
        doc.setFillColor(hexToRgb(colors[colorIndex]).r, hexToRgb(colors[colorIndex]).g, hexToRgb(colors[colorIndex]).b);
        doc.rect(x, y, cellWidth, cellHeight, 'F');
        
        doc.setTextColor(0, 0, 0);
        let sessionY = y + 5;
        
        sessions.forEach((session: TrainingSchedule, index: number) => {
          // Add time
          doc.setFontSize(10);
          doc.text(`${session.startTime} - ${session.endTime}`, x + 3, sessionY);
          
          // Add facility if available
          if (session.facility?.name) {
            doc.setFontSize(8);
            doc.text(normalizeTurkishText(session.facility.name), x + 3, sessionY + 4);
          }
          
          sessionY += 8;
        });
      }
    }
  } else {
    // If no schedule exists, display a message
    for (let i = 0; i < 7; i++) {
      const x = 14 + (i * cellWidth);
      const y = startY + 10;
      doc.setDrawColor(200, 200, 200);
      doc.rect(x, y, cellWidth, cellHeight);
    }
    
    doc.setTextColor(100, 100, 100);
    doc.text(normalizeTurkishText(t('teams.details.noSchedule')), landscape ? 150 : 105, startY + 10 + (cellHeight / 2), { align: 'center' });
  }
  
  // Add footer with date
  const today = new Date();
  const dateString = today.toLocaleDateString(t('common.locale', { defaultValue: 'en-US' }));
  doc.setFontSize(10);
  doc.setTextColor(100, 100, 100);
  doc.text(
    `${normalizeTurkishText(t('common.generatedOn'))}: ${dateString}`, 
    landscape ? 289 : 200, 
    landscape ? 200 : 287, 
    { align: 'right' }
  );
  
  return doc;
}

// Helper function to convert hex color to RGB
function hexToRgb(hex: string): { r: number, g: number, b: number } {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : { r: 0, g: 0, b: 0 };
}
