CREATE TYPE "public"."sms_status" AS ENUM('pending', 'sent', 'failed', 'cancelled');--> statement-breakpoint
CREATE TYPE "public"."sms_type" AS ENUM('payment_reminder', 'team_message', 'custom');--> statement-breakpoint
CREATE TABLE "sms_balance" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" varchar(255) NOT NULL,
	"balance" integer DEFAULT 0 NOT NULL,
	"last_updated" timestamp DEFAULT now() NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"created_by" bigint NOT NULL,
	"updated_by" bigint NOT NULL,
	CONSTRAINT "sms_balance_tenant_id_unique" UNIQUE("tenant_id")
);
--> statement-breakpoint
CREATE TABLE "sms_configurations" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" varchar(255) NOT NULL,
	"version" integer NOT NULL,
	"pending_payment_template" text NOT NULL,
	"overdue_payment_template" text NOT NULL,
	"pending_reminder_days" text NOT NULL,
	"overdue_reminder_days" text NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"created_by" bigint NOT NULL,
	"updated_by" bigint NOT NULL
);
--> statement-breakpoint
CREATE TABLE "sms_logs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" varchar(255) NOT NULL,
	"type" "sms_type" NOT NULL,
	"status" "sms_status" NOT NULL,
	"receiver" varchar(20) NOT NULL,
	"message" text NOT NULL,
	"sender_identifier" varchar(50) NOT NULL,
	"sent_at" timestamp DEFAULT now() NOT NULL,
	"message_id" varchar(255),
	"provider_response" text,
	"athlete_id" uuid,
	"payment_id" uuid,
	"team_id" uuid,
	"sender_type" varchar(20) NOT NULL,
	"sender_id" varchar(255),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "sms_logs" ADD CONSTRAINT "sms_logs_athlete_id_athletes_id_fk" FOREIGN KEY ("athlete_id") REFERENCES "public"."athletes"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sms_logs" ADD CONSTRAINT "sms_logs_payment_id_payments_id_fk" FOREIGN KEY ("payment_id") REFERENCES "public"."payments"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sms_logs" ADD CONSTRAINT "sms_logs_team_id_teams_id_fk" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE set null ON UPDATE no action;