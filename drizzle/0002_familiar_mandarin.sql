CREATE TABLE "athlete_payment_plans" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" varchar(255) NOT NULL,
	"athlete_id" uuid NOT NULL,
	"plan_id" uuid NOT NULL,
	"team_id" uuid,
	"assigned_date" date DEFAULT now() NOT NULL,
	"start_date" date NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"remaining_payments" integer NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"created_by" bigint NOT NULL,
	"updated_by" bigint NOT NULL
);
--> statement-breakpoint
ALTER TABLE "payment_plans" RENAME COLUMN "amount" TO "total_amount";--> statement-breakpoint
ALTER TABLE "payment_plans" ADD COLUMN "installment_amount" numeric(10, 2);--> statement-breakpoint
ALTER TABLE "payment_plans" ADD COLUMN "repeat_count" integer;--> statement-breakpoint
ALTER TABLE "payments" ADD COLUMN "athlete_payment_plan_id" uuid;--> statement-breakpoint
ALTER TABLE "payments" ADD COLUMN "due_date" date;--> statement-breakpoint
ALTER TABLE "athlete_payment_plans" ADD CONSTRAINT "athlete_payment_plans_athlete_id_athletes_id_fk" FOREIGN KEY ("athlete_id") REFERENCES "public"."athletes"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "athlete_payment_plans" ADD CONSTRAINT "athlete_payment_plans_plan_id_payment_plans_id_fk" FOREIGN KEY ("plan_id") REFERENCES "public"."payment_plans"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "athlete_payment_plans" ADD CONSTRAINT "athlete_payment_plans_team_id_teams_id_fk" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payments" ADD CONSTRAINT "payments_athlete_payment_plan_id_athlete_payment_plans_id_fk" FOREIGN KEY ("athlete_payment_plan_id") REFERENCES "public"."athlete_payment_plans"("id") ON DELETE set null ON UPDATE no action;