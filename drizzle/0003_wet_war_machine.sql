CREATE TYPE "public"."payment_plan_status" AS ENUM('active', 'inactive');--> statement-breakpoint
ALTER TYPE "public"."payment_status" ADD VALUE 'cancelled';--> statement-breakpoint
ALTER TABLE "payment_plan_branches" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
DROP TABLE "payment_plan_branches" CASCADE;--> statement-breakpoint
ALTER TABLE "payments" DROP CONSTRAINT "payments_plan_id_payment_plans_id_fk";
--> statement-breakpoint
ALTER TABLE "payments" ALTER COLUMN "due_date" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "athlete_payment_plans" ADD COLUMN "last_payment_date" date;--> statement-breakpoint
ALTER TABLE "payment_plans" ADD COLUMN "monthly_value" numeric(10, 2) NOT NULL;--> statement-breakpoint
ALTER TABLE "payment_plans" ADD COLUMN "assign_day" integer NOT NULL;--> statement-breakpoint
ALTER TABLE "payment_plans" ADD COLUMN "due_day" integer NOT NULL;--> statement-breakpoint
ALTER TABLE "payment_plans" ADD COLUMN "status" "payment_plan_status" DEFAULT 'active' NOT NULL;--> statement-breakpoint
ALTER TABLE "athlete_payment_plans" DROP COLUMN "start_date";--> statement-breakpoint
ALTER TABLE "athlete_payment_plans" DROP COLUMN "remaining_payments";--> statement-breakpoint
ALTER TABLE "payment_plans" DROP COLUMN "start_date";--> statement-breakpoint
ALTER TABLE "payment_plans" DROP COLUMN "end_date";--> statement-breakpoint
ALTER TABLE "payment_plans" DROP COLUMN "total_amount";--> statement-breakpoint
ALTER TABLE "payment_plans" DROP COLUMN "installment_amount";--> statement-breakpoint
ALTER TABLE "payment_plans" DROP COLUMN "repeat_count";--> statement-breakpoint
ALTER TABLE "payment_plans" DROP COLUMN "frequency";--> statement-breakpoint
ALTER TABLE "payments" DROP COLUMN "plan_id";