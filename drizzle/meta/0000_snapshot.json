{"id": "da449931-28b5-4d19-bd62-96eeebfa0aff", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.athlete_teams": {"name": "athlete_teams", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "athlete_id": {"name": "athlete_id", "type": "uuid", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": true}, "joined_at": {"name": "joined_at", "type": "date", "primaryKey": false, "notNull": true, "default": "now()"}, "left_at": {"name": "left_at", "type": "date", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"athlete_teams_athlete_id_athletes_id_fk": {"name": "athlete_teams_athlete_id_athletes_id_fk", "tableFrom": "athlete_teams", "tableTo": "athletes", "columnsFrom": ["athlete_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "athlete_teams_team_id_teams_id_fk": {"name": "athlete_teams_team_id_teams_id_fk", "tableFrom": "athlete_teams", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.athletes": {"name": "athletes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "surname": {"name": "surname", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "national_id": {"name": "national_id", "type": "<PERSON><PERSON><PERSON>(11)", "primaryKey": false, "notNull": true}, "birth_date": {"name": "birth_date", "type": "date", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "athlete_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "balance": {"name": "balance", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0'"}, "parent_name": {"name": "parent_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "parent_surname": {"name": "parent_surname", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "parent_phone": {"name": "parent_phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "parent_email": {"name": "parent_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "parent_address": {"name": "parent_address", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.branches": {"name": "branches", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.expenses": {"name": "expenses", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "expense_category", "typeSchema": "public", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "instructor_id": {"name": "instructor_id", "type": "uuid", "primaryKey": false, "notNull": false}, "facility_id": {"name": "facility_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"expenses_instructor_id_instructors_id_fk": {"name": "expenses_instructor_id_instructors_id_fk", "tableFrom": "expenses", "tableTo": "instructors", "columnsFrom": ["instructor_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "expenses_facility_id_facilities_id_fk": {"name": "expenses_facility_id_facilities_id_fk", "tableFrom": "expenses", "tableTo": "facilities", "columnsFrom": ["facility_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.facilities": {"name": "facilities", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "facility_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "total_capacity": {"name": "total_capacity", "type": "integer", "primaryKey": false, "notNull": false}, "currently_occupied": {"name": "currently_occupied", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "length": {"name": "length", "type": "numeric(8, 2)", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric(8, 2)", "primaryKey": false, "notNull": false}, "dimension_unit": {"name": "dimension_unit", "type": "dimension_unit", "typeSchema": "public", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.instructor_branches": {"name": "instructor_branches", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "instructor_id": {"name": "instructor_id", "type": "uuid", "primaryKey": false, "notNull": true}, "branch_id": {"name": "branch_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"instructor_branches_instructor_id_instructors_id_fk": {"name": "instructor_branches_instructor_id_instructors_id_fk", "tableFrom": "instructor_branches", "tableTo": "instructors", "columnsFrom": ["instructor_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "instructor_branches_branch_id_branches_id_fk": {"name": "instructor_branches_branch_id_branches_id_fk", "tableFrom": "instructor_branches", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.instructor_schools": {"name": "instructor_schools", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "instructor_id": {"name": "instructor_id", "type": "uuid", "primaryKey": false, "notNull": true}, "school_id": {"name": "school_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"instructor_schools_instructor_id_instructors_id_fk": {"name": "instructor_schools_instructor_id_instructors_id_fk", "tableFrom": "instructor_schools", "tableTo": "instructors", "columnsFrom": ["instructor_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "instructor_schools_school_id_schools_id_fk": {"name": "instructor_schools_school_id_schools_id_fk", "tableFrom": "instructor_schools", "tableTo": "schools", "columnsFrom": ["school_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.instructors": {"name": "instructors", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "surname": {"name": "surname", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "national_id": {"name": "national_id", "type": "<PERSON><PERSON><PERSON>(11)", "primaryKey": false, "notNull": false}, "birth_date": {"name": "birth_date", "type": "date", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "salary": {"name": "salary", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.item_purchases": {"name": "item_purchases", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "item_id": {"name": "item_id", "type": "uuid", "primaryKey": false, "notNull": true}, "athlete_id": {"name": "athlete_id", "type": "uuid", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true}, "total_price": {"name": "total_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "item_purchase_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "purchase_date": {"name": "purchase_date", "type": "date", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"item_purchases_item_id_items_id_fk": {"name": "item_purchases_item_id_items_id_fk", "tableFrom": "item_purchases", "tableTo": "items", "columnsFrom": ["item_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "item_purchases_athlete_id_athletes_id_fk": {"name": "item_purchases_athlete_id_athletes_id_fk", "tableFrom": "item_purchases", "tableTo": "athletes", "columnsFrom": ["athlete_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.items": {"name": "items", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "item_category", "typeSchema": "public", "primaryKey": false, "notNull": true}, "stock": {"name": "stock", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment_plan_branches": {"name": "payment_plan_branches", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "plan_id": {"name": "plan_id", "type": "uuid", "primaryKey": false, "notNull": true}, "branch_id": {"name": "branch_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"payment_plan_branches_plan_id_payment_plans_id_fk": {"name": "payment_plan_branches_plan_id_payment_plans_id_fk", "tableFrom": "payment_plan_branches", "tableTo": "payment_plans", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payment_plan_branches_branch_id_branches_id_fk": {"name": "payment_plan_branches_branch_id_branches_id_fk", "tableFrom": "payment_plan_branches", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment_plans": {"name": "payment_plans", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "frequency": {"name": "frequency", "type": "payment_frequency", "typeSchema": "public", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payments": {"name": "payments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "athlete_id": {"name": "athlete_id", "type": "uuid", "primaryKey": false, "notNull": true}, "plan_id": {"name": "plan_id", "type": "uuid", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "payment_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "type": {"name": "type", "type": "payment_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"payments_athlete_id_athletes_id_fk": {"name": "payments_athlete_id_athletes_id_fk", "tableFrom": "payments", "tableTo": "athletes", "columnsFrom": ["athlete_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payments_plan_id_payment_plans_id_fk": {"name": "payments_plan_id_payment_plans_id_fk", "tableFrom": "payments", "tableTo": "payment_plans", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.school_branches": {"name": "school_branches", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "school_id": {"name": "school_id", "type": "uuid", "primaryKey": false, "notNull": true}, "branch_id": {"name": "branch_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"school_branches_school_id_schools_id_fk": {"name": "school_branches_school_id_schools_id_fk", "tableFrom": "school_branches", "tableTo": "schools", "columnsFrom": ["school_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "school_branches_branch_id_branches_id_fk": {"name": "school_branches_branch_id_branches_id_fk", "tableFrom": "school_branches", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.schools": {"name": "schools", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "founded_year": {"name": "founded_year", "type": "integer", "primaryKey": false, "notNull": true}, "logo": {"name": "logo", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.teams": {"name": "teams", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "school_id": {"name": "school_id", "type": "uuid", "primaryKey": false, "notNull": true}, "branch_id": {"name": "branch_id", "type": "uuid", "primaryKey": false, "notNull": true}, "instructor_id": {"name": "instructor_id", "type": "uuid", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"teams_school_id_schools_id_fk": {"name": "teams_school_id_schools_id_fk", "tableFrom": "teams", "tableTo": "schools", "columnsFrom": ["school_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "teams_branch_id_branches_id_fk": {"name": "teams_branch_id_branches_id_fk", "tableFrom": "teams", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "teams_instructor_id_instructors_id_fk": {"name": "teams_instructor_id_instructors_id_fk", "tableFrom": "teams", "tableTo": "instructors", "columnsFrom": ["instructor_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.training_schedules": {"name": "training_schedules", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": true}, "facility_id": {"name": "facility_id", "type": "uuid", "primaryKey": false, "notNull": true}, "day_of_week": {"name": "day_of_week", "type": "integer", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "time", "primaryKey": false, "notNull": true}, "end_time": {"name": "end_time", "type": "time", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "bigint", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"training_schedules_team_id_teams_id_fk": {"name": "training_schedules_team_id_teams_id_fk", "tableFrom": "training_schedules", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "training_schedules_facility_id_facilities_id_fk": {"name": "training_schedules_facility_id_facilities_id_fk", "tableFrom": "training_schedules", "tableTo": "facilities", "columnsFrom": ["facility_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.athlete_status": {"name": "athlete_status", "schema": "public", "values": ["active", "inactive", "suspended"]}, "public.dimension_unit": {"name": "dimension_unit", "schema": "public", "values": ["meters", "feet"]}, "public.expense_category": {"name": "expense_category", "schema": "public", "values": ["salary", "insurance", "rent", "equipment", "other"]}, "public.facility_type": {"name": "facility_type", "schema": "public", "values": ["field", "court", "pool", "studio", "other"]}, "public.item_category": {"name": "item_category", "schema": "public", "values": ["equipment", "clothing", "accessories", "other"]}, "public.item_purchase_status": {"name": "item_purchase_status", "schema": "public", "values": ["pending", "completed", "cancelled"]}, "public.payment_frequency": {"name": "payment_frequency", "schema": "public", "values": ["monthly", "quarterly"]}, "public.payment_status": {"name": "payment_status", "schema": "public", "values": ["pending", "completed", "overdue"]}, "public.payment_type": {"name": "payment_type", "schema": "public", "values": ["fee", "equipment", "other"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}