{"errors": {"has_dependent_teams": "Instructor cannot be deleted; assigned teams exist.", "cannot_delete_athlete_with_active_payments": "Athlete cannot be deleted; unpaid payments exist.", "athlete_creation_failed": "Failed to create athlete.Please try again later or contact support.", "unique_national_id": "An athlete with this national ID already exists in the system.", "athlete_not_in_team": "Athlete is not a member of this team.", "athlete_already_in_team": "Athlete is already a member of this team.", "invalid_instructor": "Selected instructor does not exist.", "invalid_facility": "Selected facility does not exist.", "duplicate_facility_name": "A facility with this name already exists.", "capacity_exceeded": "Currently occupied cannot exceed total capacity.", "duplicate_instructor_email": "An instructor with this email already exists.", "duplicate_instructor_national_id": "An instructor with this national ID already exists.", "duplicate_item_name": "An item with this name already exists.", "has_pending_purchases": "Cannot delete item with pending item purchase payments.", "insufficient_stock": "The item has not enough stock.", "unique_plan_name": "A payment plan with this name already exists.", "assign_before_due": "Assign day must be before due day.", "cannot_delete_plan_with_assignments": "Cannot delete payment plan with active assignments.", "has_teams": "Cannot delete school with existing teams", "insufficient_balance": "Insufficient SMS balance.", "sms_disabled": "SMS service is disabled.", "missing_sender": "Sender identifier is required and no default is configured.", "sms_send_failed": "Failed to send SMS.", "no_payments_found": "No payments found for the provided IDs.", "sms_config_error": "Failed to get SMS configuration.", "no_valid_recipients": "No valid phone numbers found for the selected payments.", "no_athletes_found": "No athletes found for the specified team.", "invalid_balance": "Invalid balance amount. Balance can not be negative.", "invalid_amount": "Invalid amount. Amount must be positive.", "duplicate_team_name": "A team with this name already exists in the selected school.", "invalid_school": "Selected school does not exist.", "invalid_branch": "Selected branch does not exist.", "payment_plan_conflicts": "Some athletes already have active payment plans for this team.", "name_required_validation": "Name is required.", "name_length_validation": "Name must be between 2 and 50 characters.", "surname_required_validation": "Surname is required.", "surname_length_validation": "Surname must be between 2 and 50 characters.", "email_format_validation": "Invalid email format.", "national_id_validation": "Invalid national ID."}}