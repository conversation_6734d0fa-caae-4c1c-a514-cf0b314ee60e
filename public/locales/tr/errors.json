{"errors": {"has_dependent_teams": "Eğitmen si<PERSON>; atanmış takımlar var.", "cannot_delete_athlete_with_active_payments": "<PERSON><PERSON><PERSON>; ödenmemiş borcu bulunmaktadir.", "athlete_creation_failed": "Sporcu oluşturulamadı. Lütfen daha sonra tekrar deneyin veya destek ile iletişime geçin.", "unique_national_id": "Bu TC Kimlik No ile zaten bir sporcu sisteme kayıtlı.", "athlete_not_in_team": "Sporcu bu takımda değil.", "athlete_already_in_team": "Sporcu zaten bu takımda.", "invalid_instructor": "Seçilen eğitmen bulunamadı.", "invalid_facility": "Seçilen tesis bulunamadı.", "duplicate_facility_name": "Bu isimde bir tesis zaten mevcut.", "capacity_exceeded": "Mevcut kapasite toplam kapasiteyi aşamaz.", "duplicate_instructor_email": "Bu e-posta adresiyle zaten bir eğitmen mevcut.", "duplicate_instructor_national_id": "Bu TC Kimlik No ile zaten bir eğitmen mevcut.", "duplicate_item_name": "<PERSON>u isimde bir ürün zaten mevcut.", "has_pending_purchases": "Bekleyen satın alma ödeme işlemleri olduğu için ürün si<PERSON>mez.", "insufficient_stock": "<PERSON>rün stoku yet<PERSON>li miktarda <PERSON>.", "unique_plan_name": "Bu isimde bir ödeme planı zaten mevcut.", "assign_before_due": "Atama günü vade günü öncesi olmalıdır.", "cannot_delete_plan_with_assignments": "Atanan sporcular olduğu için ödeme planı silinemez.", "has_teams": "Bu okula baglı takım(lar) olduğu için okul silinemez.", "insufficient_balance": "Yetersiz SMS bakiyesi.", "sms_disabled": "SMS servisi aktif degil.", "missing_sender": "Gönderen tanımlayıcısı gereklidir. Varsayılan gönderici yapılandırılmamış.", "sms_send_failed": "SMS gönderilemedi.", "no_payments_found": "Belirtilen ID'ler için ödeme bulunamadı.", "sms_config_error": "SMS yapılandırması alınamadı.", "no_valid_recipients": "Secilen ödemeler için geçerli telefon numarası bulunamadı.", "no_athletes_found": "Belirtilen takıma ait sporcu bulunamadı.", "invalid_balance": "Geçersiz SMS bakiyesi. Bakiye negatif olamaz.", "invalid_amount": "Geçersiz tutar. <PERSON><PERSON> pozitif olmalıdır.", "duplicate_team_name": "Bu okulda bu isimde bir takım zaten mevcut.", "invalid_school": "Seçilen okul bulunamadı.", "invalid_branch": "Seçilen branş bulunamadı.", "payment_plan_conflicts": "Bu takım için bazı sporcuların zaten aktif ödeme planları var.", "name_required_validation": "<PERSON><PERSON><PERSON> al<PERSON>.", "name_length_validation": "İsim 2 ile 50 karakter arasında olmalıdır.", "surname_required_validation": "<PERSON><PERSON><PERSON><PERSON> al<PERSON>.", "surname_length_validation": "Soyisim 2 ile 50 karakter arasında olmalıdır.", "email_format_validation": "Geçersiz e-posta formatı.", "national_id_validation": "Geçersiz TC Kimlik No."}}