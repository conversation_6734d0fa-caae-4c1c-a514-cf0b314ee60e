{"instructors": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"title": "Eğitmen <PERSON>", "personalInfo": "<PERSON><PERSON><PERSON><PERSON>", "name": "Ad", "firstName": "Ad", "lastName": "Soyad", "surname": "Soyad", "email": "E-posta", "phone": "Telefon", "nationalId": "TC Kimlik No", "birthDate": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON>", "salary": "Maaş", "specializations": "Uzmanlık Alanları", "schools": "<PERSON><PERSON><PERSON>", "branches": "Branşlar", "quickStats": "Hızlı İstatistikler"}, "form": {"title": "Yeni Eğitmen Oluştur", "editTitle": "<PERSON><PERSON><PERSON><PERSON>", "description": "Organizasyonunuza yeni bir eğitmen ekleyin", "name": "Ad", "surname": "Soyad", "email": "E-posta", "phone": "Telefon", "nationalId": "TC Kimlik No", "birthDate": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON>", "salary": "Maaş", "branches": "Branşlar", "schools": "<PERSON><PERSON><PERSON>", "namePlaceholder": "Eğit<PERSON>in adını girin", "surnamePlaceholder": "Eğitmenin soyadını girin", "emailPlaceholder": "Eğitmenin e-postasını girin", "phonePlaceholder": "Eğitmenin telefon numarasını girin", "nationalIdPlaceholder": "TC Kimlik No girin", "birthDatePlaceholder": "<PERSON><PERSON><PERSON> tari<PERSON> se<PERSON>", "addressPlaceholder": "<PERSON><PERSON> girin", "salaryPlaceholder": "Maaş miktarını girin", "selectBranches": "Branşları seçin...", "selectSchools": "Okulları seçin..."}, "actions": {"add": "<PERSON><PERSON><PERSON><PERSON>", "addNew": "<PERSON><PERSON><PERSON><PERSON>", "create": "Eğitmen <PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "createInstructor": "Eğitmen <PERSON>", "openMenu": "Menüyü a<PERSON>"}, "messages": {"manageInstructors": "Eğitmenlerinizi ve görevlerini yönetin", "allInstructors": "<PERSON><PERSON><PERSON>", "instructorsList": "Organizasyonunuzdaki tüm eğitmenlerin listesi", "createSuccess": "Eğitmen başarıyla oluşturuldu", "createError": "Eğitmen oluşturulamadı", "loadError": "Eğitmenler yüklenemedi", "schoolCount": "{{count}} okul", "schoolCount_other": "{{count}} okul", "updateSuccess": "Başarılı", "instructorUpdated": "Eğitmen başarıyla gü<PERSON>llendi", "updateError": "Eğit<PERSON>", "updateFailed": "Eğit<PERSON>", "deleteSuccess": "Başarılı", "instructorDeleted": "<PERSON>ğit<PERSON> ba<PERSON><PERSON><PERSON><PERSON>", "deleteError": "<PERSON><PERSON><PERSON><PERSON>", "deleteConfirmTitle": "Emin misiniz?", "deleteConfirmDescription": "Bu işlem geri alınamaz. Bu kalıcı olarak {{name}} adlı eğitmeni ve tüm ilgili verileri silecektir.", "deleting": "Siliniyor..."}, "placeholders": {"searchInstructors": "Eğitmen ara...", "searchMinChars": "Arama yapmak için en az 3 karakter yazın"}}}