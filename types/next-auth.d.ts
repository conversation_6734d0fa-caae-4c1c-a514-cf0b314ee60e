import NextAuth from "next-auth";
import { JWT } from "next-auth/jwt";

declare module "next-auth" {
  interface Session {
    accessToken?: string;
    idToken?: string;
    refreshToken?: string;
    tenantId?: string; // Organization ID from Zitadel
    userId?: string; // User ID from Zitadel (as string for session)
    error?: string;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    accessToken?: string;
    idToken?: string;
    refreshToken?: string;
    expiresAt?: number;
    tenantId?: string; // Organization ID from Zitadel
    userId?: string; // User ID from Zitadel (as string for JWT)
    organization_id?: string; // Alternative organization field
    org?: string; // Alternative organization field
    urn?: any; // For nested Zitadel claims
    error?: string;
  }
}
