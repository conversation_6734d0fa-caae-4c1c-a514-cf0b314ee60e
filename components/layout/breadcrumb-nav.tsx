"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { useEntityNames } from "@/hooks/use-entity-names";
import { useEffect, useState, useMemo } from "react";
import {
  B<PERSON><PERSON>rumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Home, Loader2 } from "lucide-react";

interface BreadcrumbSegment {
  label: string;
  href?: string;
  isLast: boolean;
  isLoading?: boolean;
  entityType?: string;
  entityId?: string;
}

export function BreadcrumbNav() {
  const pathname = usePathname();
  const { t } = useSafeTranslation();
  const { getEntityName, loading: entityLoading } = useEntityNames({ enabled: true });
  const [resolvedSegments, setResolvedSegments] = useState<BreadcrumbSegment[]>([]);

  // Helper function to check if a string is a UUID
  const isUUID = (str: string): boolean => {
    return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(str);
  };

  const generateBreadcrumbs = async (): Promise<BreadcrumbSegment[]> => {
    const segments = pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbSegment[] = [];

    // Always start with Dashboard as home
    breadcrumbs.push({
      label: t('breadcrumbs.dashboard'),
      href: '/dashboard',
      isLast: false,
    });

    let currentPath = '';

    for (let index = 0; index < segments.length; index++) {
      const segment = segments[index];
      currentPath += `/${segment}`;
      const isLast = index === segments.length - 1;

      // Skip the first segment if it's already the dashboard
      if (segment === 'dashboard' && index === 0) {
        breadcrumbs[0].isLast = isLast;
        continue;
      }

      let label = segment;
      let href = isLast ? undefined : currentPath;
      let entityType: string | undefined;
      let entityId: string | undefined;

      // Handle main section names
      if (index === 0) {
        const sectionKey = `breadcrumbs.${segment}`;
        label = t(sectionKey, segment.charAt(0).toUpperCase() + segment.slice(1));
      }
      // Handle common action pages
      else if (['new', 'edit', 'sell'].includes(segment)) {
        const actionKey = `breadcrumbs.${segment}`;
        label = t(actionKey, segment.charAt(0).toUpperCase() + segment.slice(1));
      }
      // Handle UUIDs (entity IDs)
      else if (isUUID(segment)) {
        // Get the entity type from the previous segment
        entityType = segments[index - 1] || 'item';
        entityId = segment;
        
        try {
          label = await getEntityName(entityType, segment);
        } catch (error) {
          // Fallback to default format
          label = t(`breadcrumbs.${entityType}`, entityType) + ` #${segment.slice(0, 8)}`;
        }
        
        // If this is the last segment (details page), show entity name without href
        if (isLast) {
          href = undefined;
        }
      }
      // Handle special case: 'plans' within payments should redirect to payments page
      else if (segment === 'plans' && segments[0] === 'payments') {
        const actionKey = `breadcrumbs.${segment}`;
        label = t(actionKey, segment.charAt(0).toUpperCase() + segment.slice(1));
        // Redirect to payments page with plans tab
        href = isLast ? undefined : '/payments?tab=plans';
      }
      // Handle other segments (capitalize first letter)
      else {
        label = segment.charAt(0).toUpperCase() + segment.slice(1);
      }

      breadcrumbs.push({
        label,
        href,
        isLast,
        entityType,
        entityId,
      });
    }

    return breadcrumbs;
  };

  // Memoize the basic breadcrumb structure
  const basicBreadcrumbs = useMemo(() => {
    const segments = pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbSegment[] = [];

    // Always start with Dashboard as home
    breadcrumbs.push({
      label: t('breadcrumbs.dashboard'),
      href: '/dashboard',
      isLast: false,
    });

    let currentPath = '';

    segments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === segments.length - 1;

      // Skip the first segment if it's already the dashboard
      if (segment === 'dashboard' && index === 0) {
        breadcrumbs[0].isLast = isLast;
        return;
      }

      let label = segment;
      let href = isLast ? undefined : currentPath;
      let entityType: string | undefined;
      let entityId: string | undefined;

      // Handle main section names
      if (index === 0) {
        const sectionKey = `breadcrumbs.${segment}`;
        label = t(sectionKey, segment.charAt(0).toUpperCase() + segment.slice(1));
      }
      // Handle common action pages
      else if (['new', 'edit', 'sell'].includes(segment)) {
        const actionKey = `breadcrumbs.${segment}`;
        label = t(actionKey, segment.charAt(0).toUpperCase() + segment.slice(1));
      }
      // Handle UUIDs (entity IDs)
      else if (isUUID(segment)) {
        // Get the entity type from the previous segment
        entityType = segments[index - 1] || 'item';
        entityId = segment;
        
        // Show loading state initially, will be resolved later
        label = t(`breadcrumbs.${entityType}`, entityType) + ` #${segment.slice(0, 8)}`;
        
        // If this is the last segment (details page), show entity name without href
        if (isLast) {
          href = undefined;
        }
      }
      // Handle special case: 'plans' within payments should redirect to payments page
      else if (segment === 'plans' && segments[0] === 'payments') {
        const actionKey = `breadcrumbs.${segment}`;
        label = t(actionKey, segment.charAt(0).toUpperCase() + segment.slice(1));
        // Redirect to payments page with plans tab
        href = isLast ? undefined : '/payments?tab=plans';
      }
      // Handle other segments (capitalize first letter)
      else {
        label = segment.charAt(0).toUpperCase() + segment.slice(1);
      }

      breadcrumbs.push({
        label,
        href,
        isLast,
        entityType,
        entityId,
      });
    });

    return breadcrumbs;
  }, [pathname, t]);

  // Resolve entity names asynchronously
  useEffect(() => {
    const resolveEntityNames = async () => {
      const resolvedBreadcrumbs = [...basicBreadcrumbs];
      
      for (let i = 0; i < resolvedBreadcrumbs.length; i++) {
        const breadcrumb = resolvedBreadcrumbs[i];
        if (breadcrumb.entityType && breadcrumb.entityId) {
          try {
            const entityName = await getEntityName(breadcrumb.entityType, breadcrumb.entityId);
            resolvedBreadcrumbs[i] = {
              ...breadcrumb,
              label: entityName,
            };
          } catch (error) {
            // Keep the fallback label
            console.warn(`Failed to resolve entity name for ${breadcrumb.entityType}:${breadcrumb.entityId}`, error);
          }
        }
      }
      
      setResolvedSegments(resolvedBreadcrumbs);
    };

    resolveEntityNames();
  }, [basicBreadcrumbs, getEntityName]);

  // Use resolved segments or basic breadcrumbs as fallback
  const breadcrumbs = resolvedSegments.length > 0 ? resolvedSegments : basicBreadcrumbs;

  // Don't show breadcrumbs on the main dashboard page
  if (pathname === '/dashboard') {
    return null;
  }

  return (
    <div className="mb-6">
      <Breadcrumb>
        <BreadcrumbList>
          {breadcrumbs.map((breadcrumb: BreadcrumbSegment, index: number) => (
            <div key={index} className="flex items-center">
              <BreadcrumbItem>
                {breadcrumb.isLast ? (
                  <BreadcrumbPage className="flex items-center">
                    {breadcrumb.entityType && breadcrumb.entityId && entityLoading && (
                      <Loader2 className="h-3 w-3 animate-spin mr-2 text-muted-foreground" />
                    )}
                    <span className={breadcrumb.entityType && breadcrumb.entityId && entityLoading ? "opacity-75" : ""}>
                      {breadcrumb.label}
                    </span>
                  </BreadcrumbPage>
                ) : (
                  <BreadcrumbLink asChild>
                    <Link href={breadcrumb.href || '#'} className="flex items-center hover:text-foreground transition-colors">
                      {index === 0 && (
                        <Home className="h-4 w-4 mr-2" />
                      )}
                      {breadcrumb.entityType && breadcrumb.entityId && entityLoading && (
                        <Loader2 className="h-3 w-3 animate-spin mr-2 text-muted-foreground" />
                      )}
                      <span className={breadcrumb.entityType && breadcrumb.entityId && entityLoading ? "opacity-75" : ""}>
                        {breadcrumb.label}
                      </span>
                    </Link>
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
              {!breadcrumb.isLast && <BreadcrumbSeparator />}
            </div>
          ))}
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  );
}
