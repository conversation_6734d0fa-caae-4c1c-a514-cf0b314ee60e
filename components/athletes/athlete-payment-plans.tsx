"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { TenantAwareDB } from "@/lib/db";
import { 
  assignPaymentPlan, 
  getAthleteAssignedPlans, 
  deactivateAssignment 
} from "@/lib/actions/payment-plan-assignments";

interface PaymentPlan {
  id: string;
  name: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
  status: "active" | "inactive";
}

interface AthletePaymentPlan {
  id: string;
  planName: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
  assignedDate: string;
  isActive: boolean;
}

interface AthletePaymentPlansProps {
  athleteId: string;
}

export function AthletePaymentPlans({ athleteId }: AthletePaymentPlansProps) {
  const [athletePaymentPlans, setAthletePaymentPlans] = useState<AthletePaymentPlan[]>([]);
  const [availablePaymentPlans, setAvailablePaymentPlans] = useState<PaymentPlan[]>([]);
  const [selectedPlanId, setSelectedPlanId] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchAthletePaymentPlans();
    fetchAvailablePaymentPlans();
  }, [athleteId]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchAthletePaymentPlans = async () => {
    try {
      const result = await getAthleteAssignedPlans(athleteId);
      setAthletePaymentPlans(result);
    } catch (error) {
      console.error("Error fetching athlete payment plans:", error);
    }
  };

  const fetchAvailablePaymentPlans = async () => {
    try {
      const plans = await TenantAwareDB.getPaymentPlans();
      setAvailablePaymentPlans(plans);
    } catch (error) {
      console.error("Error fetching payment plans:", error);
    }
  };

  const handleAssignPaymentPlan = async () => {
    if (!selectedPlanId) return;

    setIsLoading(true);
    try {
      const result = await assignPaymentPlan({
        athleteId,
        planId: selectedPlanId,
      });

      toast({
        title: "Success",
        description: "Payment plan assigned successfully.",
      });
      setIsDialogOpen(false);
      setSelectedPlanId("");
      fetchAthletePaymentPlans();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to assign payment plan",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeactivatePaymentPlan = async (paymentPlanId: string) => {
    try {
      await deactivateAssignment(paymentPlanId);
      toast({
        title: "Success",
        description: "Payment plan deactivated successfully",
      });
      fetchAthletePaymentPlans();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to deactivate payment plan",
        variant: "destructive",
      });
    }
  };

  const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat("tr-TR", {
      style: "currency",
      currency: "TRY",
    }).format(parseFloat(amount));
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Payment Plans</h3>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>Assign Payment Plan</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Assign Payment Plan</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <Select value={selectedPlanId} onValueChange={setSelectedPlanId}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a payment plan" />
                </SelectTrigger>
                <SelectContent>
                  {availablePaymentPlans.map((plan) => (
                    <SelectItem key={plan.id} value={plan.id}>                      {plan.name} - {formatCurrency(plan.monthlyValue)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleAssignPaymentPlan}
                  disabled={!selectedPlanId || isLoading}
                >
                  {isLoading ? "Assigning..." : "Assign"}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-4">
        {athletePaymentPlans.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <p className="text-center text-muted-foreground">
                No payment plans assigned to this athlete.
              </p>
            </CardContent>
          </Card>
        ) : (
          athletePaymentPlans.map((plan) => (
            <Card key={plan.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">{plan.planName}</CardTitle>
                    <div className="flex items-center space-x-2 mt-2">
                      <Badge variant={plan.isActive ? "default" : "secondary"}>
                        {plan.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </div>
                  {plan.isActive && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeactivatePaymentPlan(plan.id)}
                    >
                      Deactivate
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Monthly Value</p>
                    <p className="font-semibold">{formatCurrency(plan.monthlyValue)}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Assign Day</p>
                    <p className="font-semibold">{plan.assignDay}th</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Due Day</p>
                    <p className="font-semibold">{plan.dueDay}th</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}
