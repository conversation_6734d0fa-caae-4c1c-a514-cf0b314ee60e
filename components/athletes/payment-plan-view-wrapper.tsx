import { getAthleteAssignedPlans } from "@/lib/actions/payment-plan-assignments";
import { PaymentPlanView } from "./payment-plan-view";

interface PaymentPlanViewWrapperProps {
  athleteId: string;
}

export async function PaymentPlanViewWrapper({ 
  athleteId 
}: PaymentPlanViewWrapperProps) {
  try {
    const assignedPlans = await getAthleteAssignedPlans(athleteId);

    return (
      <PaymentPlanView
        athletePaymentPlans={assignedPlans}
      />
    );
  } catch (error) {
    console.error("Error loading payment plan data:", error);
    return (
      <div className="text-center py-8 text-muted-foreground">
        Failed to load payment plan data. Please try again.
      </div>
    );
  }
}
