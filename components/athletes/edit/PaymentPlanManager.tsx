"use client";

import { useState, useEffect } from "react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Trash2, Plus, Calendar, ToggleLeft, ToggleRight } from "lucide-react";
import { TurkishLiraIcon } from "@/components/ui/turkish-lira-icon";
import { formatCurrency } from "@/lib/utils";
import { getPaymentPlans } from "@/lib/actions/payment-plans";
import { Skeleton } from "@/components/ui/skeleton";
import type { AthleteTeam, AthletePaymentPlan, PaymentPlan } from "@/hooks/athletes/usePaymentPlanManagement";
import {useToast} from "@/hooks/use-toast";

interface PaymentPlanManagerProps {
  athleteId: string;
  athleteTeams: AthleteTeam[];
  paymentPlanChanges: AthletePaymentPlan[];
  onPaymentPlanChangesUpdate: (changes: AthletePaymentPlan[]) => void;
}

export function PaymentPlanManager({
  athleteId,
  athleteTeams,
  paymentPlanChanges,
  onPaymentPlanChangesUpdate,
}: PaymentPlanManagerProps) {
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [availablePaymentPlans, setAvailablePaymentPlans] = useState<PaymentPlan[]>([]);
  const [selectedTeamId, setSelectedTeamId] = useState<string>("");
  const [selectedPlanId, setSelectedPlanId] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingPlans, setIsLoadingPlans] = useState(true);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // Load available payment plans
  useEffect(() => {
    const loadPaymentPlans = async () => {
      try {
        setIsLoadingPlans(true);
        setIsInitialLoad(true);
        const plans = await getPaymentPlans();
        setAvailablePaymentPlans(plans);
      } catch (error) {
        console.error("Error loading payment plans:", error);
        toast({
          title: t('common.error'),
          description: t('common.error'),
          variant: "destructive",
        });
      } finally {
        setIsLoadingPlans(false);
        // Add a small delay to show the loading state
        setTimeout(() => {
          setIsInitialLoad(false);
        }, 500);
      }
    };

    void loadPaymentPlans();
  }, [t, toast]);

  // Filter payment plans based on selected team's branch
  const selectedTeam = athleteTeams.find(team => team.teamId === selectedTeamId);
  const filteredPaymentPlans = availablePaymentPlans.filter(plan => 
    !selectedTeam || !plan.branches || 
    plan.branches.some(branch => branch.id === selectedTeam.branchId)
  );

  const handleAssignPaymentPlan = async () => {
    if (!selectedPlanId || !selectedTeamId) return;

    const selectedPlan = availablePaymentPlans.find(plan => plan.id === selectedPlanId);
    const team = athleteTeams.find(team => team.teamId === selectedTeamId);
    
    if (!selectedPlan || !team) return;

    // Check if this plan is already assigned to this team
    const existingAssignment = paymentPlanChanges.find(
      assignment => assignment.planId === selectedPlanId && assignment.teamId === selectedTeamId
    );

    if (existingAssignment) {
      toast({
        title: t('common.error'),
        description: t('payments.plans.assignments.alreadyAssigned'),
        variant: "destructive",
      });
      return;
    }

    const newAssignment: AthletePaymentPlan = {
      id: `temp-${Date.now()}`, // Temporary ID for new assignments
      planId: selectedPlan.id,
      teamId: selectedTeamId,
      assignedDate: new Date().toISOString(),
      isActive: true,
      planName: selectedPlan.name,
      monthlyValue: selectedPlan.monthlyValue,
      assignDay: selectedPlan.assignDay,
      dueDay: selectedPlan.dueDay,
      teamName: team.teamName,
      athleteName: '', // Will be set by parent component
      athleteSurname: '', // Will be set by parent component
    };

    const updatedAssignments = [...paymentPlanChanges, newAssignment];
    onPaymentPlanChangesUpdate(updatedAssignments);

    // Reset dialog state
    setSelectedTeamId("");
    setSelectedPlanId("");
    setIsDialogOpen(false);
    toast({
      title: t('common.success'),
      description: t('payments.plans.assignments.assignSuccess'),
    });
  };

  const handleRemovePaymentPlan = (assignmentId: string) => {
    const updatedAssignments = paymentPlanChanges.filter(
      assignment => assignment.id !== assignmentId
    );
    onPaymentPlanChangesUpdate(updatedAssignments);
    toast({
      title: t('common.success'),
      description: t('payments.plans.assignments.removeSuccess'),
    });
  };

  const handleTogglePaymentPlan = (assignmentId: string) => {
    const updatedAssignments = paymentPlanChanges.map(assignment => 
      assignment.id === assignmentId 
        ? { ...assignment, isActive: !assignment.isActive }
        : assignment
    );
    onPaymentPlanChangesUpdate(updatedAssignments);
    
    const assignment = paymentPlanChanges.find(a => a.id === assignmentId);
    const action = assignment?.isActive ? 'deactivated' : 'activated';
    toast({
      title: t('common.success'),
      description: t(`payments.plans.assignments.${action}`),
    });
  };

  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-9 w-40" />
      </div>
      <div className="space-y-3">
        {[1, 2, 3].map((i) => (
          <div key={i} className="border-2 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-2 flex-1">
                <div className="flex items-center gap-2">
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-5 w-16" />
                </div>
                <Skeleton className="h-4 w-24" />
                <div className="flex items-center gap-4">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-4 w-20" />
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Skeleton className="h-8 w-8" />
                <Skeleton className="h-8 w-8" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // Show loading skeleton during initial load
  if (isInitialLoad) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TurkishLiraIcon className="h-5 w-5" />
            {t('athletes.form.paymentPlans')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <LoadingSkeleton />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <TurkishLiraIcon className="h-5 w-5" />
            {t('athletes.form.paymentPlans')}
            {isLoadingPlans && !isInitialLoad && (
              <div className="ml-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
              </div>
            )}
          </CardTitle>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" disabled={athleteTeams.length === 0}>
                <Plus className="h-4 w-4 mr-2" />
                {t('payments.plans.assignments.assignNew')}
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>{t('payments.plans.assignments.assignToTeam')}</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>{t('teams.details.team')} *</Label>
                  <Select value={selectedTeamId} onValueChange={setSelectedTeamId}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('teams.details.selectTeam')} />
                    </SelectTrigger>
                    <SelectContent>
                      {athleteTeams.map((team) => (
                        <SelectItem key={team.teamId} value={team.teamId}>
                          {team.teamName} ({t(`common.branches.${team.branchName}`, { ns: 'shared' })})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>{t('payments.plans.plan')} *</Label>
                  <Select value={selectedPlanId} onValueChange={setSelectedPlanId}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('payments.plans.select')} />
                    </SelectTrigger>
                    <SelectContent>
                      {isLoadingPlans ? (
                        <div className="p-4 space-y-2">
                          <div className="text-center text-muted-foreground mb-2">
                            {t('common.loading', { ns: 'shared' })}
                          </div>
                          <Skeleton className="h-8 w-full" />
                          <Skeleton className="h-8 w-full" />
                          <Skeleton className="h-8 w-full" />
                        </div>
                      ) : filteredPaymentPlans.length === 0 ? (
                        <div className="p-2 text-center text-muted-foreground">
                          {t('payments.plans.noAvailable')}
                        </div>
                      ) : (
                        filteredPaymentPlans.map((plan) => (
                          <SelectItem key={plan.id} value={plan.id}>
                            {plan.name} - {formatCurrency(plan.monthlyValue)}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                  >
                    {t('common.actions.cancel')}
                  </Button>
                  <Button
                    onClick={handleAssignPaymentPlan}
                    disabled={isLoading || !selectedPlanId || !selectedTeamId}
                  >
                    {isLoading ? t('common.actions.saving') : t('common.actions.assign')}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {athleteTeams.length === 0 ? (
          <div className="text-center py-6">
            <p className="text-muted-foreground mb-2">
              {t('athletes.form.noTeamsForPaymentPlans')}
            </p>
            <p className="text-sm text-muted-foreground">
              {t('athletes.form.addToTeamFirst')}
            </p>
          </div>
        ) : paymentPlanChanges.length === 0 ? (
          <div className="text-center py-6">
            <TurkishLiraIcon className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
            <p className="text-muted-foreground mb-3">
              {t('payments.plans.assignments.noAssignments')}
            </p>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  {t('payments.plans.assignments.assignFirst')}
                </Button>
              </DialogTrigger>
            </Dialog>
          </div>
        ) : (
          <div className="space-y-3">
            {paymentPlanChanges.map((assignment) => (
              <Card key={assignment.id} className="border-2">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{assignment.planName}</h4>
                        <Badge variant={assignment.isActive ? "default" : "secondary"}>
                          {assignment.isActive ? t('common.status.active') : t('common.status.inactive')}
                        </Badge>
                      </div>
                      <div className="text-sm text-muted-foreground space-y-1">
                        <p>{assignment.teamName}</p>
                        <div className="flex items-center gap-4">
                          <span className="flex items-center gap-1">
                            <TurkishLiraIcon className="h-3 w-3" />
                            {formatCurrency(assignment.monthlyValue)}
                          </span>
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {t('payments.plans.dueDay')}: {assignment.dueDay}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleTogglePaymentPlan(assignment.id)}
                        className="p-2"
                      >
                        {assignment.isActive ? (
                          <ToggleRight className="h-4 w-4 text-green-600" />
                        ) : (
                          <ToggleLeft className="h-4 w-4 text-gray-400" />
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemovePaymentPlan(assignment.id)}
                        className="text-red-600 hover:text-red-700 p-2"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
