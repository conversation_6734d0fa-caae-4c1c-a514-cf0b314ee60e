import { But<PERSON> } from "@/components/ui/button";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { Loader2 } from "lucide-react";

interface AthleteFormActionsProps {
  isValid: boolean;
  isSaving: boolean;
  onSave: () => void;
  onCancel: () => void;
}

export function AthleteFormActions({ 
  isValid, 
  isSaving, 
  onSave, 
  onCancel 
}: AthleteFormActionsProps) {
  const { t } = useSafeTranslation();

  return (
    <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
      <Button
        type="button"
        variant="outline"
        onClick={onCancel}
        disabled={isSaving}
      >
        {t('common.actions.cancel')}
      </Button>
      <Button
        type="button"
        onClick={onSave}
        disabled={!isValid || isSaving}
      >
        {isSaving ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            {t('common.actions.saving')}
          </>
        ) : (
          t('common.actions.save')
        )}
      </Button>
    </div>
  );
}
