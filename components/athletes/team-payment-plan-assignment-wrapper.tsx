import { getTeams } from "@/lib/actions/teams";
import { getAthleteTeams } from "@/lib/actions/athlete-teams";
import { getAthleteAssignedPlans } from "@/lib/actions/payment-plan-assignments";
import { getPaymentPlans } from "@/lib/actions/payment-plans";
import { TeamPaymentPlanAssignment } from "./team-payment-plan-assignment";

interface TeamPaymentPlanAssignmentWrapperProps {
  athleteId: string;
}

export async function TeamPaymentPlanAssignmentWrapper({ 
  athleteId 
}: TeamPaymentPlanAssignmentWrapperProps) {
  try {
    const [teams, athleteTeamsData, plans, assignedPlans] = await Promise.all([
      getTeams(),
      getAthleteTeams(athleteId),
      getPaymentPlans(),
      getAthleteAssignedPlans(athleteId),
    ]);

    return (
      <TeamPaymentPlanAssignment
        athleteId={athleteId}
        athleteTeams={athleteTeamsData}
        availableTeams={teams}
        athletePaymentPlans={assignedPlans}
        availablePaymentPlans={plans}
      />
    );
  } catch (error) {
    console.error("Error loading team payment plan data:", error);
    return (
      <div className="text-center py-8 text-muted-foreground">
        Failed to load payment plan assignment data. Please try again.
      </div>
    );
  }
}
