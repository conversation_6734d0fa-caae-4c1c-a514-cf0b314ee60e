"use client";

import { DashboardCard } from "@/components/ui/dashboard-card";
import {
  School,
  Users,
  BarChart3,
  User,
  CreditCard,
  MapPin,
  MessageSquare
} from "lucide-react";
import { TurkishLiraIcon } from "@/components/ui/turkish-lira-icon";
import { useTranslation } from "react-i18next";

export function DashboardWidgets() {
  const { t } = useTranslation(['shared', 'dashboard']);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      <DashboardCard
        title={t("nav.schools", { ns: 'shared' })}
        description={t("dashboard.cards.schools", { ns: 'dashboard' })}
        icon={School}
        href="/schools"
      />
      <DashboardCard
        title={t("nav.instructors", { ns: 'shared' })}
        description={t("dashboard.cards.instructors", { ns: 'dashboard' })}
        icon={Users}
        href="/instructors"
      />
      <DashboardCard
        title={t("nav.teams", { ns: 'shared' })}
        description={t("dashboard.cards.teams", { ns: 'dashboard' })}
        icon={BarChart3}
        href="/teams"
      />
      <DashboardCard
        title={t("nav.athletes", { ns: 'shared' })}
        description={t("dashboard.cards.athletes", { ns: 'dashboard' })}
        icon={User}
        href="/athletes"
      />
      <DashboardCard
        title={t("nav.payments", { ns: 'shared' })}
        description={t("dashboard.cards.payments", { ns: 'dashboard' })}
        icon={CreditCard}
        href="/payments"
      />
      <DashboardCard
        title={t("nav.expenses", { ns: 'shared' })}
        description={t("dashboard.cards.expenses", { ns: 'dashboard' })}
        icon={TurkishLiraIcon}
        href="/expenses"
      />
      <DashboardCard
        title={t("nav.facilities", { ns: 'shared' })}
        description={t("dashboard.cards.facilities", { ns: 'dashboard' })}
        icon={MapPin}
        href="/facilities"
      />
      <DashboardCard
        title={t("nav.sms", { ns: 'shared' })}
        description={t("dashboard.cards.sms", { ns: 'dashboard' })}
        icon={MessageSquare}
        href="/sms"
      />
    </div>
  );
}