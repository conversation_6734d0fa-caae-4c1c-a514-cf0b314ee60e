'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { MessageSquare, AlertTriangle, Loader2 } from 'lucide-react';
import { useSafeTranslation } from '@/hooks/use-safe-translation';
import { getPaymentsForSms } from '@/lib/actions/sms';
import { Payment } from '@/lib/types';
import PaymentReminderDialog from './payment-reminder-dialog';

export default function QuickSmsActions() {
  const { t } = useSafeTranslation();
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadPayments() {
      try {
        const result = await getPaymentsForSms();
        setPayments((result.data || []) as unknown as Payment[]);
      } catch (err) {
        console.error('Error loading payments:', err);
        setError('Error loading payments');
      } finally {
        setLoading(false);
      }
    }

    loadPayments();
  }, []);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5" />
            <span>{t('sms:quickActions.title')}</span>
          </CardTitle>
          <CardDescription>
            {t('sms:quickActions.description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5" />
            <span>{t('sms:quickActions.title')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  // Filter payments that can receive SMS reminders
  const eligiblePayments = payments.filter(payment => 
    (payment.status === 'pending' || payment.status === 'overdue') &&
    payment.athlete?.parentPhone
  );

  const pendingPayments = eligiblePayments.filter(payment => payment.status === 'pending');
  const overduePayments = eligiblePayments.filter(payment => payment.status === 'overdue');

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <MessageSquare className="h-5 w-5" />
          <span>{t('sms:quickActions.title')}</span>
        </CardTitle>
        <CardDescription>
          {t('sms:quickActions.description')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="text-2xl font-bold text-blue-600">{pendingPayments.length}</div>
              <div className="text-sm text-blue-700">{t('sms:sending.bulk.pending')}</div>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg border border-red-200">
              <div className="text-2xl font-bold text-red-600">{overduePayments.length}</div>
              <div className="text-sm text-red-700">{t('sms:sending.bulk.overdue')}</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="text-2xl font-bold text-green-600">{eligiblePayments.length}</div>
              <div className="text-sm text-green-700">{t('sms:sending.bulk.totalEligible')}</div>
            </div>
          </div>

          {/* Quick Action Buttons */}
          <div className="grid gap-4 md:grid-cols-2">
            {/* Send to All Pending */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">{t('sms:sending.bulk.pending')}</h4>
              {pendingPayments.length > 0 ? (
                <PaymentReminderDialog
                  payments={pendingPayments}
                  trigger={
                    <Button className="w-full" variant="outline">
                      <MessageSquare className="h-4 w-4 mr-2" />
                      {t('sms:sending.bulk.sendToAllPending')} ({pendingPayments.length})
                    </Button>
                  }
                />
              ) : (
                <Button className="w-full" variant="outline" disabled>
                  <MessageSquare className="h-4 w-4 mr-2" />
                  {t('sms:sending.bulk.sendToAllPending')} (0)
                </Button>
              )}
            </div>

            {/* Send to All Overdue */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">{t('sms:sending.bulk.overdue')}</h4>
              {overduePayments.length > 0 ? (
                <PaymentReminderDialog
                  payments={overduePayments}
                  trigger={
                    <Button className="w-full" variant="destructive">
                      <MessageSquare className="h-4 w-4 mr-2" />
                      {t('sms:sending.bulk.sendToAllOverdue')} ({overduePayments.length})
                    </Button>
                  }
                />
              ) : (
                <Button className="w-full" variant="destructive" disabled>
                  <MessageSquare className="h-4 w-4 mr-2" />
                  {t('sms:sending.bulk.sendToAllOverdue')} (0)
                </Button>
              )}
            </div>
          </div>



          {/* Important Notes */}
          {eligiblePayments.length > 0 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1">
                  <p className="font-medium">{t('sms:sending.bulk.notes.title')}</p>
                  <ul className="text-xs space-y-1 ml-4">
                    <li>• {t('sms:sending.bulk.notes.items.0')}</li>
                    <li>• {t('sms:sending.bulk.notes.items.1')}</li>
                    <li>• {t('sms:sending.bulk.notes.items.2')}</li>
                    <li>• {t('sms:sending.bulk.notes.items.3')}</li>
                  </ul>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* No Eligible Payments */}
          {eligiblePayments.length === 0 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {t('sms:sending.bulk.noEligible')}
              </AlertDescription>
            </Alert>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
