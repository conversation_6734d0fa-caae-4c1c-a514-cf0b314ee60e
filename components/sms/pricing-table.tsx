'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { getSmsPricingTiers } from '@/lib/actions/sms';
import { useSafeTranslation } from '@/hooks/use-safe-translation';
import { Loader2 } from 'lucide-react';
import { TurkishLiraIcon } from '@/components/ui/turkish-lira-icon';

interface SmsPricingTier {
  minCredits: number;
  maxCredits: number | null;
  pricePerCredit: number; // in cents
  description: string;
}

export default function PricingTable() {
  const { t } = useSafeTranslation();
  const [tiers, setTiers] = useState<SmsPricingTier[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadPricingTiers() {
      try {
        const result = await getSmsPricingTiers();
        if (result.success && result.data) {
          setTiers(result.data);
        } else {
          setError('Failed to load pricing tiers');
        }
      } catch (err) {
        console.error('Error loading pricing tiers:', err);
        setError('Failed to load pricing tiers');
      } finally {
        setLoading(false);
      }
    }

    loadPricingTiers();
  }, []);

  const formatPrice = (priceInCents: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(priceInCents / 100);
  };

  const getTierBadgeVariant = (index: number) => {
    switch (index) {
      case 0: return 'secondary';
      case 1: return 'default';
      case 2: return 'outline';
      case 3: return 'destructive';
      default: return 'secondary';
    }
  };

  const getTierName = (index: number) => {
    switch (index) {
      case 0: return t('sms:balance.pricing.tiers.starter');
      case 1: return t('sms:balance.pricing.tiers.standard');
      case 2: return t('sms:balance.pricing.tiers.professional');
      case 3: return t('sms:balance.pricing.tiers.enterprise');
      default: return t('sms:balance.pricing.tiers.custom');
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TurkishLiraIcon className="h-5 w-5" />
            <span>{t('sms:balance.pricing.title')}</span>
          </CardTitle>
          <CardDescription>
            {t('sms:balance.pricing.description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>{t('sms:common.loading')}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TurkishLiraIcon className="h-5 w-5" />
            <span>{t('sms:balance.pricing.title')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            {error}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <TurkishLiraIcon className="h-5 w-5" />
          <span>{t('sms:balance.pricing.title')}</span>
        </CardTitle>
        <CardDescription>
          {t('sms:balance.pricing.description')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {tiers.map((tier, index) => (
            <div
              key={index}
              className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
            >
              <div className="flex items-center space-x-4">
                <Badge variant={getTierBadgeVariant(index)}>
                  {getTierName(index)}
                </Badge>
                <div>
                  <div className="font-medium">
                    {tier.minCredits.toLocaleString()} - {tier.maxCredits ? tier.maxCredits.toLocaleString() : '∞'} {t('sms:common.credits')}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {tier.description}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-bold text-lg">
                  {formatPrice(tier.pricePerCredit)}
                </div>
                <div className="text-sm text-muted-foreground">
                  {t('sms:balance.pricing.perCredit')}
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-6 p-4 bg-muted rounded-lg">
          <h4 className="font-medium mb-2">{t('sms:balance.pricing.notes.title')}</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• {t('sms:balance.pricing.notes.graduated')}</li>
            <li>• {t('sms:balance.pricing.notes.automatic')}</li>
            <li>• {t('sms:balance.pricing.notes.noExpiry')}</li>
            <li>• {t('sms:balance.pricing.notes.multiSms')}</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
