'use client';

import { useState, useTransition } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CreditCard, AlertTriangle } from 'lucide-react';
import { purchaseSmsCredits } from '@/lib/actions/sms';
import { useSafeTranslation } from '@/hooks/use-safe-translation';
import {useToast} from "@/hooks/use-toast";

const paymentSchema = z.object({
  cardNumber: z.string().min(16, 'Card number must be at least 16 digits'),
  expiryDate: z.string().regex(/^\d{2}\/\d{2}$/, 'Expiry date must be in MM/YY format'),
  cvv: z.string().min(3, 'CVV must be at least 3 digits').max(4, 'CVV must be at most 4 digits'),
  cardHolder: z.string().min(2, 'Card holder name is required'),
});

type PaymentFormData = z.infer<typeof paymentSchema>;

interface PaymentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  credits: number;
  totalPrice: number;
  pricePerCredit: number;
  onSuccess: () => void;
}

export default function PaymentDialog({
  open,
  onOpenChange,
  credits,
  totalPrice,
  pricePerCredit,
  onSuccess
}: PaymentDialogProps) {
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [isPending, startTransition] = useTransition();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      cardNumber: '1234 5678 9012 3456',
      expiryDate: '12/25',
      cvv: '123',
      cardHolder: 'John Doe',
    }
  });

  const formatPrice = (priceInCents: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(priceInCents / 100);
  };

  const onSubmit = (data: PaymentFormData) => {
    startTransition(async () => {
      try {
        const result = await purchaseSmsCredits({
          credits,
          expectedPrice: totalPrice,
          paymentData: data
        });

        if (result.success) {
          toast({
            title: t('common.success'),
            description:t('sms:balance.payment.success').replace(/{credits}/g, credits.toString())
          });
          reset();
          onOpenChange(false);
          onSuccess();
        }else{
          let errorDescriptionKey = '';
          if(result.errorType == 'BusinessRuleError'){
            errorDescriptionKey = `errors.${result.error}`;
          }else{
            errorDescriptionKey = 'sms:balance.payment.error';
          }
          toast({
            title: t('common.error'),
            description:t(errorDescriptionKey),
            variant: "destructive",
          });
        }
      } catch (error) {
        toast({
          title: t('common.error'),
          description:t('sms:balance.payment.error'),
          variant: "destructive",
        });
      }
    });
  };

  const handleClose = () => {
    if (!isPending) {
      reset();
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <CreditCard className="h-5 w-5" />
            <span>{t('sms:balance.payment.title')}</span>
          </DialogTitle>
          <DialogDescription>
            {t('sms:balance.payment.description')}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Purchase Summary */}
          <div className="bg-muted p-4 rounded-lg space-y-2">
            <div className="flex justify-between text-sm">
              <span>{t('sms:balance.payment.summary.credits')}</span>
              <span className="font-medium">{credits.toLocaleString()}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>{t('sms:balance.payment.summary.pricePerCredit')}</span>
              <span className="font-medium">{formatPrice(pricePerCredit)}</span>
            </div>
            <div className="border-t pt-2 flex justify-between font-medium">
              <span>{t('sms:balance.payment.summary.total')}</span>
              <span className="text-lg">{formatPrice(totalPrice)}</span>
            </div>
          </div>

          {/* Payment Form */}
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="cardNumber">{t('sms:balance.payment.cardNumber')}</Label>
                <Input
                  id="cardNumber"
                  {...register('cardNumber')}
                  placeholder="1234 5678 9012 3456"
                  disabled
                  className="bg-muted"
                />
                {errors.cardNumber && (
                  <p className="text-sm text-red-500 mt-1">{errors.cardNumber.message}</p>
                )}
              </div>
              <div>
                <Label htmlFor="expiryDate">{t('sms:balance.payment.expiryDate')}</Label>
                <Input
                  id="expiryDate"
                  {...register('expiryDate')}
                  placeholder="MM/YY"
                  disabled
                  className="bg-muted"
                />
                {errors.expiryDate && (
                  <p className="text-sm text-red-500 mt-1">{errors.expiryDate.message}</p>
                )}
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="cvv">{t('sms:balance.payment.cvv')}</Label>
                <Input
                  id="cvv"
                  {...register('cvv')}
                  placeholder="123"
                  disabled
                  className="bg-muted"
                />
                {errors.cvv && (
                  <p className="text-sm text-red-500 mt-1">{errors.cvv.message}</p>
                )}
              </div>
              <div>
                <Label htmlFor="cardHolder">{t('sms:balance.payment.cardHolder')}</Label>
                <Input
                  id="cardHolder"
                  {...register('cardHolder')}
                  placeholder="John Doe"
                  disabled
                  className="bg-muted"
                />
                {errors.cardHolder && (
                  <p className="text-sm text-red-500 mt-1">{errors.cardHolder.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Demo Notice */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {t('sms:balance.payment.demoNote')}
            </AlertDescription>
          </Alert>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose} disabled={isPending}>
              {t('sms:common.cancel')}
            </Button>
            <Button type="submit" disabled={isPending}>
              {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {t('sms:balance.payment.payNow')} {formatPrice(totalPrice)}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
