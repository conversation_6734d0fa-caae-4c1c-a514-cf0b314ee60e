import { cn } from "@/lib/utils";

interface SkeletonDashboardCardProps {
  className?: string;
}

export function SkeletonDashboardCard({ className }: SkeletonDashboardCardProps) {
  return (
    <div className={cn("p-6 rounded-lg shadow-md bg-card", className)}>
      <div className="flex items-center justify-between mb-4">
        <div className="h-6 w-1/2 bg-muted rounded animate-pulse"></div>
        <div className="h-6 w-6 bg-muted rounded-full animate-pulse"></div>
      </div>
      <div className="h-4 w-full bg-muted rounded animate-pulse"></div>
    </div>
  );
}