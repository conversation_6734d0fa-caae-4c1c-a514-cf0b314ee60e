"use client";

import React, { useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/ui/data-table';
import {
  ListHeader,
  SearchBar,
  PaginationControls,
  PaginatedListContainer,
} from '@/components/ui/paginated-list';
import {
  usePaginatedList,
  PaginationData,
  SearchParams,
  UsePaginatedListOptions,
} from '@/hooks/use-paginated-list';

interface GenericListPageProps<TData> {
  // Data
  data: TData[];
  pagination: PaginationData;
  columns: ColumnDef<TData, any>[];
  
  // Configuration
  title: string;
  description?: string;
  basePath: string;
  initialSearchParams: SearchParams;
  
  // Customization
  actions?: React.ReactNode;
  filters?: React.ReactNode;
  searchPlaceholder?: string;
  
  // Options
  paginationOptions?: Partial<UsePaginatedListOptions>;
  
  // Loading state
  isLoading?: boolean;
  
  // Custom content
  customHeader?: React.ReactNode;
  customFooter?: React.ReactNode;
  
  // Table options
  showSearch?: boolean;
  showPagination?: boolean;
}

export function GenericListPage<TData>({
  data,
  pagination,
  columns,
  title,
  description,
  basePath,
  initialSearchParams,
  actions,
  filters,
  searchPlaceholder,
  paginationOptions = {},
  isLoading = false,
  customHeader,
  customFooter,
  showSearch = true,
  showPagination = true,
}: GenericListPageProps<TData>) {
  const {
    localSearch,
    setLocalSearch,
    currentLimit,
    isSearchValid,
    isSearching,
    isClient,
    handlePageChange,
    handleLimitChange,
    handleSearchTrigger,
  } = usePaginatedList(initialSearchParams, {
    basePath,
    ...paginationOptions,
  });

  // Add loading state for table operations
  const [isTableLoading, setIsTableLoading] = useState(false);

  // Don't render until client-side hydration is complete
  if (!isClient) {
    return (
      <div className="container mx-auto py-10">
        <PaginatedListContainer isLoading={true}>
          <div />
        </PaginatedListContainer>
      </div>
    );
  }

  const searchBar = showSearch ? (
    <SearchBar
      value={localSearch}
      onChange={setLocalSearch}
      onSearch={handleSearchTrigger}
      placeholder={searchPlaceholder}
      isValid={isSearchValid}
      isSearching={isSearching}
      minLength={paginationOptions.searchMinLength || 3}
      showLoadingOnType={false}
    />
  ) : null;

  return (
    <div className="container mx-auto py-10">
      <div className="space-y-8">
        {/* Custom Header or Default Header */}
        {customHeader || (
          <ListHeader
            title={title}
            description={description}
            actions={actions}
            searchBar={searchBar}
            filters={filters}
          />
        )}

        {/* Data Table */}
        <PaginatedListContainer isLoading={isLoading || isTableLoading}>
          <DataTable
            columns={columns}
            data={data}
            showPagination={false}
          />
        </PaginatedListContainer>

        {/* Pagination Controls */}
        {showPagination && !isLoading && (
          <PaginationControls
            pagination={pagination}
            currentLimit={currentLimit}
            onPageChange={handlePageChange}
            onLimitChange={handleLimitChange}
          />
        )}

        {/* Custom Footer */}
        {customFooter}
      </div>
    </div>
  );
}

// Specialized versions for common use cases
interface EntityListPageProps<TData> extends Omit<GenericListPageProps<TData>, 'basePath' | 'title'> {
  entityName: string;
  entityNamePlural: string;
  basePath: string;
}

export function EntityListPage<TData>({
  entityName,
  entityNamePlural,
  basePath,
  ...props
}: EntityListPageProps<TData>) {
  return (
    <GenericListPage
      {...props}
      basePath={basePath}
      title={entityNamePlural}
      description={`Manage your ${entityNamePlural.toLowerCase()}`}
    />
  );
}

// Export types for external use
export type { GenericListPageProps, EntityListPageProps };
