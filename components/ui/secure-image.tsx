"use client";

/**
 * SecureImage Component
 *
 * This component uses regular <img> tags instead of Next.js <Image> component
 * because our secure images require authentication headers that Next.js Image
 * component cannot provide. The images are served through our secure API route
 * (/api/secure-images) which validates user authentication and tenant isolation.
 *
 * Features client-side caching to reduce server requests and improve performance.
 * Cached images are stored as blob URLs with automatic expiration and cleanup.
 *
 * ESLint warnings about using <img> are intentionally suppressed since we
 * cannot use Next.js Image component for authenticated image serving.
 */

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { cn } from '@/lib/utils';
import { useCachedImage } from '@/hooks/use-cached-image';
import type { LucideIcon } from 'lucide-react';

interface SecureImageProps {
  src: string;
  alt: string;
  className?: string;
  fill?: boolean;
  priority?: boolean;
  sizes?: string;
  width?: number;
  height?: number;
  onLoad?: () => void;
  onError?: () => void;
  enableCache?: boolean; // New prop to control caching
  placeholderIcon?: LucideIcon; // Icon to show when image is missing or broken
}

export function SecureImage({
  src,
  alt,
  className,
  fill = false,
  priority = false,
  sizes,
  width,
  height,
  onLoad,
  onError,
  enableCache = true,
  placeholderIcon: PlaceholderIcon,
}: SecureImageProps) {
  const { data: session, status } = useSession();
  const [hasError, setHasError] = useState(false);

  // Use cached image hook for secure images
  const {
    src: cachedSrc,
    isLoading: isCacheLoading,
    error: cacheError,
    isFromCache,
    retry: retryCache,
    cacheImage,
  } = useCachedImage(src, {
    enabled: enableCache,
    priority,
  });

  const handleLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const imgElement = e.currentTarget;

    // Cache the image after successful load (only for secure images)
    if (enableCache && src.includes('/api/secure-images/') && !isFromCache) {
      cacheImage(imgElement);
    }

    onLoad?.();
  };

  const handleError = (e: any) => {
    setHasError(true);
    onError?.();

    // If cache is enabled and this was a cached image, try to retry
    if (enableCache && isFromCache) {
      retryCache();
    }
  };

  // If no src provided, show placeholder
  if (!src) {
    return (
      <div
        className={cn(
          "bg-muted flex flex-col items-center justify-center text-muted-foreground",
          fill ? "absolute inset-0" : "",
          className
        )}
        style={!fill ? { width, height } : undefined}
      >
        {PlaceholderIcon ? (
          <>
            <PlaceholderIcon className="h-8 w-8 mb-2" />
            <span className="text-xs">No image</span>
          </>
        ) : (
          <span className="text-sm">No image</span>
        )}
      </div>
    );
  }

  // Check if this is a secure local image that requires authentication
  const isSecureLocalImage = src.includes('/api/secure-images/');

  // For secure local images, check authentication
  if (isSecureLocalImage) {
    // If still loading authentication, show loading
    if (status === 'loading') {
      return (
        <div
          className={cn(
            "bg-muted animate-pulse flex items-center justify-center",
            fill ? "absolute inset-0" : "",
            className
          )}
          style={!fill ? { width, height } : undefined}
        >
          <span className="text-muted-foreground text-sm">Loading...</span>
        </div>
      );
    }

    // If not authenticated, show error
    if (status === 'unauthenticated') {
      return (
        <div
          className={cn(
            "bg-muted flex items-center justify-center text-muted-foreground",
            fill ? "absolute inset-0" : "",
            className
          )}
          style={!fill ? { width, height } : undefined}
        >
          <span className="text-sm">Authentication required</span>
        </div>
      );
    }
  }

  // Show error state if image failed to load
  if (hasError) {
    return (
      <div
        className={cn(
          "bg-muted flex flex-col items-center justify-center text-muted-foreground",
          fill ? "absolute inset-0" : "",
          className
        )}
        style={!fill ? { width, height } : undefined}
      >
        {PlaceholderIcon ? (
          <>
            <PlaceholderIcon className="h-8 w-8 mb-2" />
            <span className="text-xs">Failed to load</span>
          </>
        ) : (
          <span className="text-sm">Failed to load</span>
        )}
      </div>
    );
  }

  // Determine which src to use (cached or original)
  const imageSrc = enableCache && cachedSrc ? cachedSrc : src;

  // Render the actual image
  if (fill) {
    // For fill mode, render image directly without wrapper div
    return (
      // eslint-disable-next-line @next/next/no-img-element
      <img
        src={imageSrc}
        alt={alt}
        className={cn("absolute inset-0 w-full h-full", className)}
        onLoad={handleLoad}
        onError={handleError}
        loading={priority ? "eager" : "lazy"}
        title={enableCache && isFromCache ? "Loaded from cache" : undefined}
      />
    );
  }

  // For non-fill mode, use wrapper div
  return (
    <div>
      {/* eslint-disable-next-line @next/next/no-img-element */}
      <img
        src={imageSrc}
        alt={alt}
        className={className}
        style={{ width, height }}
        onLoad={handleLoad}
        onError={handleError}
        loading={priority ? "eager" : "lazy"}
        title={enableCache && isFromCache ? "Loaded from cache" : undefined}
      />
    </div>
  );
}
