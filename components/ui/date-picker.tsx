"use client";

import * as React from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useSafeTranslation } from "@/hooks/use-safe-translation";

interface DatePickerProps {
  date?: Date;
  onSelect: (date: Date | undefined) => void;
  placeholder?: string;
  className?: string;
}

export function DatePicker({ date, onSelect, placeholder = "Pick a date", className }: DatePickerProps) {
  const { t } = useSafeTranslation();
  const [isOpen, setIsOpen] = React.useState(false);
  const [inputValue, setInputValue] = React.useState("");
  const [calendarDate, setCalendarDate] = React.useState<Date>(date || new Date());

  // Update input value when date prop changes
  React.useEffect(() => {
    if (date) {
      setInputValue(format(date, "yyyy-MM-dd"));
      setCalendarDate(date);
    } else {
      setInputValue("");
    }
  }, [date]);

  const handleInputChange = (value: string) => {
    setInputValue(value);

    // Try to parse the input as a date
    if (value.match(/^\d{4}-\d{2}-\d{2}$/)) {
      const parsedDate = new Date(value + 'T00:00:00');
      if (!isNaN(parsedDate.getTime())) {
        onSelect(parsedDate);
        setCalendarDate(parsedDate);
      }
    } else if (value === "") {
      onSelect(undefined);
    }
  };

  const handleInputBlur = () => {
    // If input is invalid, reset to current date or empty
    if (inputValue && !inputValue.match(/^\d{4}-\d{2}-\d{2}$/)) {
      if (date) {
        setInputValue(format(date, "yyyy-MM-dd"));
      } else {
        setInputValue("");
      }
    }
  };

  const handleCalendarSelect = (selectedDate: Date | undefined) => {
    onSelect(selectedDate);
    if (selectedDate) {
      setCalendarDate(selectedDate);
    }
    setIsOpen(false);
  };

  const handleMonthChange = (increment: number) => {
    const newDate = new Date(calendarDate);
    newDate.setMonth(newDate.getMonth() + increment);
    setCalendarDate(newDate);
  };

  const handleYearChange = (year: string) => {
    const newDate = new Date(calendarDate);
    newDate.setFullYear(parseInt(year));
    setCalendarDate(newDate);
  };

  const handleMonthSelect = (month: string) => {
    const newDate = new Date(calendarDate);
    newDate.setMonth(parseInt(month));
    setCalendarDate(newDate);
  };

  const currentYear = calendarDate.getFullYear();
  const currentMonth = calendarDate.getMonth();

  // Generate year options (current year ± 50 years)
  const yearOptions = Array.from({ length: 101 }, (_, i) => currentYear - 50 + i);

  // Month names with i18n
  const monthNames = [
    t('common.months.january', { ns: 'shared' }),
    t('common.months.february', { ns: 'shared' }),
    t('common.months.march', { ns: 'shared' }),
    t('common.months.april', { ns: 'shared' }),
    t('common.months.may', { ns: 'shared' }),
    t('common.months.june', { ns: 'shared' }),
    t('common.months.july', { ns: 'shared' }),
    t('common.months.august', { ns: 'shared' }),
    t('common.months.september', { ns: 'shared' }),
    t('common.months.october', { ns: 'shared' }),
    t('common.months.november', { ns: 'shared' }),
    t('common.months.december', { ns: 'shared' })
  ];

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <div className="relative">
          <Input
            type="text"
            value={date ? format(date, "dd.MM.yyyy") : ""}
            placeholder={placeholder}
            className={cn("pr-10 cursor-pointer", className)}
            readOnly
          />
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
            type="button"
          >
            <CalendarIcon className="h-4 w-4" />
          </Button>
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
          <div className="p-3 space-y-3">
            {/* Direct input field inside popup */}
            <Input
              type="date"
              value={inputValue}
              onChange={(e) => handleInputChange(e.target.value)}
              onBlur={handleInputBlur}
              placeholder="YYYY-MM-DD"
              className="w-full"
            />

            {/* Year and Month selectors */}
            <div className="flex items-center justify-between space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleMonthChange(-1)}
                type="button"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>

              <div className="flex space-x-2">
                <Select value={currentMonth.toString()} onValueChange={handleMonthSelect}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {monthNames.map((month, index) => (
                      <SelectItem key={index} value={index.toString()}>
                        {month}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={currentYear.toString()} onValueChange={handleYearChange}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {yearOptions.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handleMonthChange(1)}
                type="button"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            {/* Calendar */}
            <Calendar
              mode="single"
              selected={date}
              onSelect={handleCalendarSelect}
              month={calendarDate}
              onMonthChange={setCalendarDate}
              initialFocus
            />
          </div>
        </PopoverContent>
      </Popover>
  );
}