import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Plus, Trash2, Clock, AlertTriangle } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import type { TrainingScheduleForm } from "@/hooks/teams/useScheduleManagement";
import type { Facility } from "@/lib/types";

const dayOptions = [
  { value: 1, label: "Monday" },
  { value: 2, label: "Tuesday" },
  { value: 3, label: "Wednesday" },
  { value: 4, label: "Thursday" },
  { value: 5, label: "Friday" },
  { value: 6, label: "Saturday" },
  { value: 0, label: "Sunday" },
];

interface TrainingScheduleManagerProps {
  schedules: TrainingScheduleForm[];
  facilities: Facility[];
  selectedScheduleIndex: number;
  onAddSchedule: () => void;
  onRemoveSchedule: (index: number) => void;
  onUpdateSchedule: (index: number, field: keyof TrainingScheduleForm, value: string | number) => void;
  onSelectSchedule: (index: number) => void;
  hasConflict: (schedule: TrainingScheduleForm, scheduleIndex?: number) => boolean;
  getConflictInfo?: (schedule: TrainingScheduleForm, scheduleIndex?: number) => {
    hasConflict: boolean;
    isInternal?: boolean;
    conflictIndex?: number;
    conflictingTeam?: string;
  };
}

export function TrainingScheduleManager({
  schedules,
  facilities,
  selectedScheduleIndex,
  onAddSchedule,
  onRemoveSchedule,
  onUpdateSchedule,
  onSelectSchedule,
  hasConflict,
  getConflictInfo,
}: TrainingScheduleManagerProps) {
  const { t } = useSafeTranslation();

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            {t('teams.details.schedule')}
          </CardTitle>
          <Button type="button" variant="outline" size="sm" onClick={onAddSchedule}>
            <Plus className="h-4 w-4 mr-2" />
            {t('teams.schedule.addSlot')}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {schedules.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Clock className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>{t('teams.details.noSchedule')}</p>
            <Button type="button" variant="outline" onClick={onAddSchedule} className="mt-2">
              <Plus className="h-4 w-4 mr-2" />
              {t('teams.schedule.addSlot')}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {schedules.map((schedule, index) => {
              const conflict = hasConflict(schedule, index);
              const conflictInfo = getConflictInfo ? getConflictInfo(schedule, index) : { hasConflict: conflict };
              const isSelected = selectedScheduleIndex === index;
              
              return (
                <div key={index}>
                  <div 
                    className={`grid grid-cols-1 md:grid-cols-5 gap-4 p-4 border rounded-lg cursor-pointer transition-colors ${
                      isSelected 
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-950' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => onSelectSchedule(index)}
                  >
                    <div>
                      <label className="text-sm font-medium">{t('teams.schedule.day')}</label>
                      <Select
                        value={schedule.dayOfWeek.toString()}
                        onValueChange={(value) => onUpdateSchedule(index, 'dayOfWeek', parseInt(value))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {dayOptions.map((day) => (
                            <SelectItem key={day.value} value={day.value.toString()}>
                              {t(`teams.days.${day.label.toLowerCase()}`)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium">{t('teams.schedule.startTime')}</label>
                      <Input
                        type="time"
                        value={schedule.startTime}
                        onChange={(e) => onUpdateSchedule(index, 'startTime', e.target.value)}
                        className={conflict ? "border-red-500" : ""}
                      />
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium">{t('teams.schedule.endTime')}</label>
                      <Input
                        type="time"
                        value={schedule.endTime}
                        onChange={(e) => onUpdateSchedule(index, 'endTime', e.target.value)}
                        className={conflict ? "border-red-500" : ""}
                      />
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium">{t('teams.schedule.facility')} ({t('common.optional')})</label>
                      <Select
                        value={schedule.facilityId || "none"}
                        onValueChange={(value) => onUpdateSchedule(index, 'facilityId', value === "none" ? "" : value)}
                      >
                        <SelectTrigger className={conflict ? "border-red-500" : ""}>
                          <SelectValue placeholder={t('facilities.placeholders.selectFacility')} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">{t('common.actions.none')}</SelectItem>
                          {facilities.map((facility) => (
                            <SelectItem key={facility.id} value={facility.id}>
                              {facility.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="flex items-end">
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent row selection when deleting
                          onRemoveSchedule(index);
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  {/* Conflict Warning */}
                  {conflict && (
                    <div className="mt-2 flex items-center space-x-2 text-red-600 text-sm">
                      <AlertTriangle className="h-4 w-4" />
                      <span>
                        {conflictInfo.isInternal 
                          ? t("teams.schedule.internalConflictWarning")
                          : conflictInfo.conflictingTeam
                            ? t("teams.schedule.conflictWith", { team: conflictInfo.conflictingTeam })
                            : t("teams.schedule.conflictWarning")
                        }
                      </span>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
