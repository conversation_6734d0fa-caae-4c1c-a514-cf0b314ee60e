"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import type { Team, School, Instructor, Facility } from "@/lib/types";

// Import the new smaller components
import { TeamBasicForm } from "./TeamBasicForm";
import { TrainingScheduleManager } from "./TrainingScheduleManager";
import { FacilityAvailabilityCard } from "./FacilityAvailabilityCard";
import { TeamFormActions } from "./TeamFormActions";

// Import the custom hooks
import { useTeamForm } from "@/hooks/teams/useTeamForm";
import { useScheduleManagement } from "@/hooks/teams/useScheduleManagement";
import { useTeamOperations } from "@/hooks/teams/useTeamOperations";

interface TeamEditClientProps {
  team: Team;
  schools: School[];
  instructors: Instructor[];
  facilities: Facility[];
}

export default function TeamEditClient({ team, schools, instructors, facilities }: TeamEditClientProps) {
  const { t } = useSafeTranslation();

  // Initialize custom hooks
  const {
    form,
    selectedSchool,
    branches,
    filteredInstructors,
    handleSchoolChange,
  } = useTeamForm({ team, schools, instructors });

  const {
    trainingSchedules,
    selectedScheduleIndex,
    selectedFacilityForAvailability,
    hasConflict,
    getConflictInfo,
    addTrainingSchedule,
    removeTrainingSchedule,
    updateTrainingSchedule,
    selectSchedule,
    checkForConflicts,
    getSelectedFacilitySchedule,
  } = useScheduleManagement({
    initialSchedules: team.trainingSchedule?.map(schedule => ({
      dayOfWeek: schedule.dayOfWeek,
      startTime: schedule.startTime,
      endTime: schedule.endTime,
      facilityId: schedule.facilityId || '',
    })) || [],
    facilities,
    teamId: team.id,
  });

  const { loading, updateTeam, navigateBack } = useTeamOperations({ teamId: team.id });

  const handleSubmit = async () => {
    const formData = form.getValues();
    const hasConflicts = checkForConflicts();
    await updateTeam(formData, trainingSchedules, hasConflicts);
  };

  return (
    <div className="space-y-6">
      <Button variant="ghost" asChild>
        <Link href={`/teams/${team.id}`}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('common.actions.back')}
        </Link>
      </Button>

      <div className="grid gap-6">
        {/* Team Details */}
        <Card>
          <CardHeader>
            <CardTitle>{t('teams.edit')}</CardTitle>
          </CardHeader>
          <CardContent>
            <TeamBasicForm
              form={form}
              schools={schools}
              branches={branches}
              filteredInstructors={filteredInstructors}
              selectedSchool={selectedSchool}
              onSchoolChange={handleSchoolChange}
              onSubmit={handleSubmit}
              loading={loading}
            />
          </CardContent>
        </Card>

        {/* Training Schedule */}
        <TrainingScheduleManager
          schedules={trainingSchedules}
          facilities={facilities}
          selectedScheduleIndex={selectedScheduleIndex}
          onAddSchedule={addTrainingSchedule}
          onRemoveSchedule={removeTrainingSchedule}
          onUpdateSchedule={updateTrainingSchedule}
          onSelectSchedule={selectSchedule}
          hasConflict={hasConflict}
          getConflictInfo={getConflictInfo}
        />

        {/* Facility Availability Section */}
        <FacilityAvailabilityCard
          selectedFacilityId={selectedFacilityForAvailability}
          facilities={facilities}
          selectedSchedule={getSelectedFacilitySchedule()}
          teamId={team.id}
        />

        {/* Action Buttons */}
        <TeamFormActions
          onCancel={navigateBack}
          onSave={handleSubmit}
          loading={loading}
        />
      </div>
    </div>
  );
}
