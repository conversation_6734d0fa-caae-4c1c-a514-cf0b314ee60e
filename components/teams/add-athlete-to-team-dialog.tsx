"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Plus, Users, CreditCard } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { calculateProratedAmount, getRemainingDaysInMonth, getTotalDaysInMonth } from "@/lib/proration-utils";
import { Input } from "@/components/ui/input";
import { addAthleteToTeamWithPaymentPlan, checkAthleteInTeam, getAvailablePaymentPlansForTeam } from "@/lib/actions/athlete-team-management";
import { getAthletes } from "@/lib/actions";
import {useToast} from "@/hooks/use-toast";

interface PaymentPlan {
  id: string;
  name: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
  status: "active" | "inactive";
  branches?: { id: string; name: string; description: string | null; }[];
}

interface Athlete {
  id: string;
  name: string;
  surname: string;
  status: string;
  teams?: string[];
}

interface AddAthleteToTeamDialogProps {
  teamId: string;
  teamName: string;
  teamBranchId: string;
  existingAthleteIds: string[];
  onAthleteAdded?: () => void;
}

export function AddAthleteToTeamDialog({
  teamId,
  teamName,
  teamBranchId,
  existingAthleteIds,
  onAthleteAdded
}: AddAthleteToTeamDialogProps) {
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedAthleteId, setSelectedAthleteId] = useState("");
  const [shouldAssignPaymentPlan, setShouldAssignPaymentPlan] = useState(false);
  const [useProrated, setUseProrated] = useState(false);
  const [proratedAmount, setProratedAmount] = useState("");
  const [calculatedProrated, setCalculatedProrated] = useState<number | null>(null);
  const [selectedPaymentPlanId, setSelectedPaymentPlanId] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [availableAthletes, setAvailableAthletes] = useState<Athlete[]>([]);
  const [availablePaymentPlans, setAvailablePaymentPlans] = useState<PaymentPlan[]>([]);
  const [loadingData, setLoadingData] = useState(false);

  const loadDialogData = useCallback(async () => {
    setLoadingData(true);
    try {
      const [allAthletes, paymentPlans] = await Promise.all([
        getAthletes(),
        getAvailablePaymentPlansForTeam(teamBranchId)
      ]);

      // Filter out athletes that are already in the team and inactive athletes
      const filteredAthletes = (allAthletes as any[]).filter((athlete: any) => 
        !existingAthleteIds.includes(athlete.id) && 
        athlete.status === 'active'
      );

      setAvailableAthletes(filteredAthletes);
      setAvailablePaymentPlans(paymentPlans || []);
    } catch (error) {
      console.error("Error loading dialog data:", error);
      toast({
        title: t('common.error'),
        description: t('common.error'),
        variant: "destructive",
      });
    } finally {
      setLoadingData(false);
    }
  }, [teamBranchId, existingAthleteIds, t, toast]);

  // Load data when dialog opens
  useEffect(() => {
    if (isOpen) {
      loadDialogData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]); // Only depend on isOpen to avoid infinite loop

  // Calculate prorated amount when useProrated changes or payment plan is selected
  useEffect(() => {
    if (useProrated && shouldAssignPaymentPlan && selectedPaymentPlanId) {
      const selectedPlan = availablePaymentPlans.find(plan => plan.id === selectedPaymentPlanId);
      if (selectedPlan) {
        const monthlyAmount = parseFloat(selectedPlan.monthlyValue);
        const prorated = calculateProratedAmount(monthlyAmount);
        setCalculatedProrated(prorated);
        setProratedAmount(prorated.toFixed(2));
      }
    } else if (useProrated) {
      setCalculatedProrated(null);
      setProratedAmount("");
    }
  }, [useProrated, shouldAssignPaymentPlan, selectedPaymentPlanId, availablePaymentPlans]);

  const handleAddAthlete = async () => {
    if (!selectedAthleteId) {
      toast({
        title: t('common.error'),
        description: t('teams.management.selectAthlete'),
        variant: "destructive",
      });
      return;
    }

    if (shouldAssignPaymentPlan && !selectedPaymentPlanId) {
      toast({
        title: t('common.error'),
        description: t('teams.management.selectPaymentPlan'),
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      // Double-check if athlete is already in team
      const checkResult = await checkAthleteInTeam(selectedAthleteId, teamId);
      
      if (checkResult) {
        toast({
          title: t('common.error'),
          description: t('teams.management.athleteAlreadyInTeam'),
          variant: "destructive",
        });
        return;
      }

      const result = await addAthleteToTeamWithPaymentPlan({
        athleteId: selectedAthleteId,
        teamId,
        paymentPlanId: shouldAssignPaymentPlan ? selectedPaymentPlanId : undefined,
        assignPaymentPlan: shouldAssignPaymentPlan,
        useProrated: shouldAssignPaymentPlan ? useProrated : false,
        customProratedAmount: useProrated && proratedAmount ? proratedAmount : undefined,
        locale: 'tr' // You might want to get this from a context
      });

      if (result.success) {
        const selectedAthlete = availableAthletes.find(a => a.id === selectedAthleteId);
        const selectedPlan = availablePaymentPlans.find(p => p.id === selectedPaymentPlanId);
        
        let successMessage = t('teams.management.athleteAddedSuccess', {
          athleteName: `${selectedAthlete?.name} ${selectedAthlete?.surname}`,
          teamName
        });

        if (shouldAssignPaymentPlan && selectedPlan) {
          successMessage += ` ${t('teams.management.withPaymentPlan', { 
            planName: selectedPlan.name 
          })}`;
        }
        toast({
          title: t('common.success'),
          description: successMessage,
        });
        
        // Reset form
        setSelectedAthleteId("");
        setSelectedPaymentPlanId("");
        setShouldAssignPaymentPlan(false);
        setUseProrated(false);
        setProratedAmount("");
        setCalculatedProrated(null);
        setShouldAssignPaymentPlan(false);
        setIsOpen(false);
        
        // Notify parent component
        onAthleteAdded?.();
      } else {
        let errorDescriptionKey = '';
        if(result.errorType == 'BusinessRuleError'){
          errorDescriptionKey = `errors.${result.error}`;
        }else{
          errorDescriptionKey = 'athletes.management.addAthleteError';
        }
        toast({
          title: t('common.error'),
          description: t(errorDescriptionKey),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error adding athlete to team:", error);
      toast({
        title: t('common.error'),
        description: t('teams.management.addAthleteError'),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat("tr-TR", {
      style: "currency",
      currency: "TRY",
    }).format(parseFloat(amount));
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      setIsOpen(open);
      // Reset form when dialog closes
      if (!open) {
        setSelectedAthleteId("");
        setSelectedPaymentPlanId("");
        setShouldAssignPaymentPlan(false);
        setUseProrated(false);
        setProratedAmount("");
        setCalculatedProrated(null);
        setAvailableAthletes([]);
        setAvailablePaymentPlans([]);
      }
    }}>
      <DialogTrigger asChild>
        <Button size="sm">
          <Plus className="h-4 w-4 mr-2" />
          {t('teams.management.addAthlete')}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {t('teams.management.addAthleteToTeam')}
          </DialogTitle>
          <DialogDescription>
            {t('teams.management.addAthleteDescription', { teamName })}
          </DialogDescription>
        </DialogHeader>

        {loadingData ? (
          <div className="space-y-4">
            <div className="h-10 bg-muted animate-pulse rounded-md" />
            <div className="h-10 bg-muted animate-pulse rounded-md" />
            <div className="h-10 bg-muted animate-pulse rounded-md" />
          </div>
        ) : (
          <div className="space-y-4">
            {/* Athlete Selection */}
            <div className="space-y-2">
              <Label>{t('athletes.title')} *</Label>
              <Select value={selectedAthleteId} onValueChange={setSelectedAthleteId}>
                <SelectTrigger>
                  <SelectValue placeholder={t('teams.management.selectAthleteToAdd')} />
                </SelectTrigger>
                <SelectContent>
                  {availableAthletes.length === 0 ? (
                    <div className="p-2 text-center text-muted-foreground">
                      {t('teams.management.noAvailableAthletes')}
                    </div>
                  ) : (
                    availableAthletes.map((athlete) => (
                      <SelectItem key={athlete.id} value={athlete.id}>
                        {athlete.name} {athlete.surname}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* Payment Plan Assignment Option */}
            <Card className="border-dashed">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Checkbox
                    id="assignPaymentPlan"
                    checked={shouldAssignPaymentPlan}
                    onCheckedChange={(checked) => setShouldAssignPaymentPlan(checked === true)}
                  />
                  <Label htmlFor="assignPaymentPlan" className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    {t('teams.management.assignPaymentPlan')}
                  </Label>
                </div>

                {shouldAssignPaymentPlan && (
                  <div className="space-y-2">
                    <Label>{t('payments.plans.plan')} *</Label>
                    <Select value={selectedPaymentPlanId} onValueChange={setSelectedPaymentPlanId}>
                      <SelectTrigger>
                        <SelectValue placeholder={t('payments.plans.select')} />
                      </SelectTrigger>
                      <SelectContent>
                        {availablePaymentPlans.length === 0 ? (
                          <div className="p-2 text-center text-muted-foreground">
                            {t('teams.management.noAvailablePaymentPlans')}
                          </div>
                        ) : (
                          availablePaymentPlans.map((plan) => (
                            <SelectItem key={plan.id} value={plan.id}>
                              {plan.name} - {formatCurrency(plan.monthlyValue)}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    {shouldAssignPaymentPlan && availablePaymentPlans.length === 0 && (
                      <p className="text-sm text-muted-foreground">
                        {t('teams.management.noPaymentPlansForBranch')}
                      </p>
                    )}
                    
                    {/* Prorated Balance Option */}
                    {shouldAssignPaymentPlan && selectedPaymentPlanId && (
                      <div className="mt-3 p-3 bg-muted/50 rounded-md">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="useProrated"
                            checked={useProrated}
                            onCheckedChange={(checked) => setUseProrated(checked === true)}
                          />
                          <Label htmlFor="useProrated" className="text-sm font-normal">
                            {t('athletes.proratedBalance', 'Apply prorated balance for remaining days of the month')}
                          </Label>
                        </div>
                        
                        {useProrated && (
                          <div className="mt-3 space-y-3">
                            {/* Calculation Details */}
                            {calculatedProrated !== null && (
                              <div className="p-3 bg-background rounded border text-sm">
                                <div className="space-y-2 text-left">
                                  <div className="flex justify-between">
                                    <span><strong>{t('athletes.selectedPlan', 'Selected Payment Plan')}:</strong></span>
                                  </div>
                                  <div className="pl-4 text-muted-foreground">
                                    {availablePaymentPlans.find(p => p.id === selectedPaymentPlanId)?.name} - {parseFloat(availablePaymentPlans.find(p => p.id === selectedPaymentPlanId)?.monthlyValue || '0').toFixed(2)} {t('common.currency', 'TL')}/{t('common.month', 'month')}
                                  </div>
                                  <div className="flex justify-between">
                                    <span><strong>{t('athletes.remainingDays', 'Remaining Days')}:</strong></span>
                                    <span>{getRemainingDaysInMonth()} / {getTotalDaysInMonth()}</span>
                                  </div>
                                  <div className="flex justify-between font-medium text-primary border-t pt-2">
                                    <span><strong>{t('athletes.calculatedAmount', 'Calculated Prorated Amount')}:</strong></span>
                                    <span>{calculatedProrated.toFixed(2)} {t('common.currency', 'TL')}</span>
                                  </div>
                                </div>
                              </div>
                            )}
                            
                            {/* Editable Prorated Amount */}
                            <div className="space-y-2">
                              <Label htmlFor="proratedAmount" className="text-sm">
                                {t('athletes.proratedAmountLabel', 'Prorated Amount')}
                              </Label>
                              <Input
                                id="proratedAmount"
                                type="number"
                                step="0.01"
                                min="0"
                                value={proratedAmount}
                                onChange={(e) => setProratedAmount(e.target.value)}
                                placeholder={t('athletes.enterProratedAmount', 'Enter prorated amount')}
                                className="text-sm"
                              />
                              <p className="text-xs text-muted-foreground">
                                {t('athletes.proratedEditHint', 'You can modify the automatically calculated amount or enter a custom value.')}
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsOpen(false)}
              >
                {t('common.actions.cancel')}
              </Button>
              <Button
                onClick={handleAddAthlete}
                disabled={
                  isLoading || 
                  !selectedAthleteId || 
                  availableAthletes.length === 0 ||
                  (shouldAssignPaymentPlan && !selectedPaymentPlanId)
                }
              >
                {isLoading ? t('common.actions.saving') : t('common.actions.add')}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
