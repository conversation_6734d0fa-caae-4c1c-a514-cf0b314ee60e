import { useState } from "react";
import { useRouter } from "next/navigation";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { updateAthlete } from "@/lib/actions";
import { persistPaymentPlanChanges } from "@/components/athletes/payment-plan-persistence";
import type { AthleteFormData } from "./useAthleteForm";
import type { AthleteTeam, AthletePaymentPlan } from "./usePaymentPlanManagement";
import {useToast} from "@/hooks/use-toast";

interface UseAthleteOperationsProps {
  athleteId: string;
}

export function useAthleteOperations({ athleteId }: UseAthleteOperationsProps) {
  const router = useRouter();
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);

  const saveAthlete = async (
    formData: AthleteFormData,
    athleteTeams: AthleteTeam[],
    originalPaymentPlans: AthletePaymentPlan[],
    paymentPlanChanges: AthletePaymentPlan[]
  ): Promise<boolean> => {
    setIsSaving(true);
    try {
      // Update athlete basic info
      await updateAthlete(athleteId, {
        name: formData.name,
        surname: formData.surname,
        nationalId: formData.nationalId,
        birthDate: formData.birthDate,
        registrationDate: formData.registrationDate,
        parentName: formData.parent.name || undefined,
        parentSurname: formData.parent.surname || undefined,
        parentPhone: formData.parent.phone || undefined,
        parentEmail: formData.parent.email || undefined,
        parentAddress: formData.parent.address || undefined,
      });

      // Handle payment plan changes persistence
      const paymentPlanResult = await persistPaymentPlanChanges(
        athleteId,
        athleteTeams,
        originalPaymentPlans,
        paymentPlanChanges
      );

      if (!paymentPlanResult.success) {
        throw new Error(paymentPlanResult.error || 'Failed to save payment plan changes');
      }
      toast({
        title: t('common.success'),
        description: t('athletes.messages.updateSuccess'),
      });
      return true;
    } catch (error) {
      console.error("Error updating athlete:", error);
      toast({
        title: t('common.error'),
        description: t('athletes.messages.updateError'),
        variant: "destructive",
      });
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  const navigateToAthlete = () => {
    router.push(`/athletes/${athleteId}`);
  };

  const navigateToList = () => {
    router.push('/athletes');
  };

  return {
    isSaving,
    saveAthlete,
    navigateToAthlete,
    navigateToList,
  };
}
