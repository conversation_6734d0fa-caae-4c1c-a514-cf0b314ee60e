import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import axios from 'axios';

export function useAuthRedirect() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'loading') return; // Still loading

    if (!session) {
      router.push('/auth/signin');
      return;
    }

    // Check if there's a refresh error
    if (session.error === 'RefreshAccessTokenError') {
      console.warn('Token refresh failed, signing out user');
      signOut({ callbackUrl: '/auth/signin' });
      return;
    }

    // Set token cookies when session is available
    const setTokenCookies = async () => {
      try {
        await axios.get('/api/auth/set-tokens', {
          withCredentials: true,
        });
      } catch (error) {
        console.error('Error setting token cookies:', error);
        // If setting tokens fails, it might indicate an invalid session
        if (axios.isAxiosError(error) && error.response?.status === 401) {
          console.warn('Unauthorized, signing out user');
          signOut({ callbackUrl: '/auth/signin' });
        }
      }
    };

    setTokenCookies();
  }, [session, status, router]);

  return { session, status };
}
