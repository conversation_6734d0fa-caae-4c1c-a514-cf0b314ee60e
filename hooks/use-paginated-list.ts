"use client";

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useDebounce } from './use-debounce';

export interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface SearchParams {
  page?: string;
  limit?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  [key: string]: string | undefined;
}

export interface UsePaginatedListOptions {
  basePath: string;
  defaultSortBy?: string;
  defaultSortOrder?: 'asc' | 'desc';
  defaultLimit?: string;
  searchMinLength?: number;
  searchDebounceMs?: number;
  enableClientSideHydration?: boolean;
}

export interface UsePaginatedListReturn {
  // Current state
  currentPage: number;
  currentLimit: string;
  currentSearch: string;
  currentSortBy: string;
  currentSortOrder: 'asc' | 'desc';
  
  // Local state for inputs
  localSearch: string;
  setLocalSearch: (value: string) => void;
  
  // Validation
  isSearchValid: boolean;
  isSearching: boolean;
  
  // Client-side hydration
  isClient: boolean;
  
  // Handlers
  handlePageChange: (page: number) => void;
  handleLimitChange: (limit: string) => void;
  handleSortChange: (sortBy: string, sortOrder?: 'asc' | 'desc') => void;
  handleSearchTrigger: () => void;
  updateURL: (params: Record<string, string | undefined>) => void;
  
  // URL state
  searchParams: URLSearchParams;
}

export function usePaginatedList(
  initialSearchParams: SearchParams,
  options: UsePaginatedListOptions
): UsePaginatedListReturn {
  const {
    basePath,
    defaultSortBy = 'createdAt',
    defaultSortOrder = 'desc',
    defaultLimit = '10',
    searchMinLength = 3,
    searchDebounceMs = 500,
    enableClientSideHydration = true,
  } = options;

  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Client-side hydration state
  const [isClient, setIsClient] = useState(!enableClientSideHydration);
  
  useEffect(() => {
    if (enableClientSideHydration) {
      setIsClient(true);
    }
  }, [enableClientSideHydration]);

  // Local search state
  const [localSearch, setLocalSearch] = useState(
    initialSearchParams.search || searchParams.get('search') || ''
  );
  const [prevLocalSearch, setPrevLocalSearch] = useState(localSearch);

  // Current URL parameters (read-only from URL)
  const currentPage = parseInt(searchParams.get('page') || '1', 10);
  const currentLimit = searchParams.get('limit') || defaultLimit;
  const currentSearch = searchParams.get('search') || '';
  const currentSortBy = searchParams.get('sortBy') || defaultSortBy;
  const currentSortOrder = (searchParams.get('sortOrder') as 'asc' | 'desc') || defaultSortOrder;

  // Search validation
  const isSearchValid = useMemo(() => {
    return localSearch.trim().length === 0 || localSearch.trim().length >= searchMinLength;
  }, [localSearch, searchMinLength]);

  // Debounced search with configurable delay
  const debouncedSearch = useDebounce(localSearch, searchDebounceMs);
  const [isSearching, setIsSearching] = useState(false);

  // URL update function
  const updateURL = useCallback((newParams: Record<string, string | undefined>) => {
    const params = new URLSearchParams(searchParams.toString());
    
    // Remove undefined values and update params
    Object.entries(newParams).forEach(([key, value]) => {
      if (value === undefined || value === '') {
        params.delete(key);
      } else {
        params.set(key, value);
      }
    });

    // Only reset to page 1 when filters/search/sort/limit change, but NOT when page is explicitly being changed
    const shouldResetPage = !('page' in newParams);
    if (shouldResetPage) {
      params.set('page', '1');
    }

    const finalURL = `${basePath}?${params.toString()}`;
    router.push(finalURL);
  }, [router, searchParams, basePath]);

  // Debounced search effect
  useEffect(() => {
    // Only trigger if debouncedSearch actually changed and meets validation
    if (debouncedSearch !== prevLocalSearch) {
      setPrevLocalSearch(debouncedSearch);

      const shouldTriggerSearch = debouncedSearch.trim().length === 0 || debouncedSearch.trim().length >= searchMinLength;

      if (shouldTriggerSearch) {
        updateURL({ search: debouncedSearch.trim() || undefined });
      }
    }
  }, [debouncedSearch, prevLocalSearch, updateURL, searchMinLength]);

  // Handlers
  const handlePageChange = useCallback((page: number) => {
    updateURL({ page: page.toString() });
  }, [updateURL]);

  const handleLimitChange = useCallback((limit: string) => {
    updateURL({ limit });
  }, [updateURL]);

  const handleSortChange = useCallback((sortBy: string, sortOrder?: 'asc' | 'desc') => {
    updateURL({ 
      sortBy, 
      sortOrder: sortOrder || (currentSortOrder === 'asc' ? 'desc' : 'asc')
    });
  }, [updateURL, currentSortOrder]);

  const handleSearchTrigger = useCallback(() => {
    setIsSearching(true);
    updateURL({ search: localSearch.trim() || undefined });
    // Reset searching state after a short delay
    setTimeout(() => setIsSearching(false), 500);
  }, [updateURL, localSearch]);

  return {
    // Current state
    currentPage,
    currentLimit,
    currentSearch,
    currentSortBy,
    currentSortOrder,
    
    // Local state
    localSearch,
    setLocalSearch,
    
    // Validation
    isSearchValid,
    isSearching,
    
    // Client-side hydration
    isClient,
    
    // Handlers
    handlePageChange,
    handleLimitChange,
    handleSortChange,
    handleSearchTrigger,
    updateURL,
    
    // URL state
    searchParams,
  };
}
