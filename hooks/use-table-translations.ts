import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';

interface TableTranslations {
  previous: string;
  next: string;
  noResults: string;
  searchPlaceholder: string;
}

const fallbackTranslations: TableTranslations = {
  previous: 'Previous',
  next: 'Next',
  noResults: 'No results.',
  searchPlaceholder: 'Search...',
};

export function useTableTranslations(): TableTranslations {
  const { t, i18n } = useTranslation();
  const [translations, setTranslations] = useState<TableTranslations>(fallbackTranslations);

  useEffect(() => {
    const updateTranslations = () => {
      console.log('Updating table translations:', {
        language: i18n.language,
        isInitialized: i18n.isInitialized,
        hasResourceBundle: i18n.hasResourceBundle(i18n.language, 'shared'),
        tableNext: t('common.table.next', { ns: 'shared' }),
        tablePrevious: t('common.table.previous', { ns: 'shared' })
      });

      // Check if translations are actually loaded
      if (i18n.isInitialized && i18n.hasResourceBundle(i18n.language, 'shared')) {
        const newTranslations = {
          previous: t('common.table.previous', { defaultValue: fallbackTranslations.previous, ns: 'shared' }),
          next: t('common.table.next', { defaultValue: fallbackTranslations.next, ns: 'shared' }),
          noResults: t('common.table.noResults', { defaultValue: fallbackTranslations.noResults, ns: 'shared' }),
          searchPlaceholder: t('common.table.searchPlaceholder', { defaultValue: fallbackTranslations.searchPlaceholder, ns: 'shared' }),
        };
        console.log('Setting new translations:', newTranslations);
        setTranslations(newTranslations);
      } else {
        console.log('Using fallback translations');
      }
    };

    updateTranslations();

    // Update when language changes
    i18n.on('languageChanged', updateTranslations);
    i18n.on('loaded', updateTranslations);

    return () => {
      i18n.off('languageChanged', updateTranslations);
      i18n.off('loaded', updateTranslations);
    };
  }, [t, i18n]);

  return translations;
}
