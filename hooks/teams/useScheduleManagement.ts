import { useState, useEffect } from "react";
import { getAllFacilitySchedules } from "@/lib/actions";
import { checkScheduleConflict, timeRangesOverlap } from "@/lib/schedule-utils";
import type { Facility } from "@/lib/types";

export interface TrainingScheduleForm {
  dayOfWeek: number;
  startTime: string;
  endTime: string;
  facilityId: string;
}

interface UseScheduleManagementProps {
  initialSchedules: TrainingScheduleForm[];
  facilities: Facility[];
  teamId: string;
}

export function useScheduleManagement({ 
  initialSchedules, 
  facilities, 
  teamId 
}: UseScheduleManagementProps) {
  const [trainingSchedules, setTrainingSchedules] = useState<TrainingScheduleForm[]>(initialSchedules);
  const [facilitySchedules, setFacilitySchedules] = useState<any[]>([]);
  const [selectedScheduleIndex, setSelectedScheduleIndex] = useState<number>(0); // Track selected schedule
  const [selectedFacilityForAvailability, setSelectedFacilityForAvailability] = useState<string>("");

  // Load facility schedules on component mount
  useEffect(() => {
    async function loadFacilitySchedules() {
      try {
        const schedules = await getAllFacilitySchedules();
        setFacilitySchedules(schedules);
      } catch (error) {
        console.error("Failed to load facility schedules:", error);
      }
    }
    loadFacilitySchedules();
  }, []);

  // Function to check if a training schedule has conflicts
  const hasConflict = (schedule: TrainingScheduleForm, scheduleIndex?: number) => {
    if (!schedule.startTime || !schedule.endTime) {
      return false;
    }

    // Check for internal conflicts (same team, overlapping schedules)
    const internalConflict = trainingSchedules.some((otherSchedule, index) => {
      // Skip if it's the same schedule being checked
      if (scheduleIndex !== undefined && index === scheduleIndex) {
        return false;
      }
      
      // Check if schedules overlap on the same day
      if (otherSchedule.dayOfWeek === schedule.dayOfWeek) {
        return timeRangesOverlap(
          schedule.startTime,
          schedule.endTime,
          otherSchedule.startTime,
          otherSchedule.endTime
        );
      }
      return false;
    });

    if (internalConflict) {
      return true;
    }

    // Check for facility conflicts with other teams
    if (!schedule.facilityId) {
      return false; // No facility selected, so no facility conflict
    }

    const facilityConflictResult = checkScheduleConflict(
      facilitySchedules,
      schedule.facilityId,
      schedule.dayOfWeek,
      schedule.startTime,
      schedule.endTime,
      teamId // Exclude current team from conflict check
    );
    
    return facilityConflictResult.hasConflict;
  };

  // Function to get detailed conflict information
  const getConflictInfo = (schedule: TrainingScheduleForm, scheduleIndex?: number) => {
    if (!schedule.startTime || !schedule.endTime) {
      return { hasConflict: false };
    }

    // Check for internal conflicts first
    const internalConflictIndex = trainingSchedules.findIndex((otherSchedule, index) => {
      if (scheduleIndex !== undefined && index === scheduleIndex) {
        return false;
      }
      
      if (otherSchedule.dayOfWeek === schedule.dayOfWeek) {
        return timeRangesOverlap(
          schedule.startTime,
          schedule.endTime,
          otherSchedule.startTime,
          otherSchedule.endTime
        );
      }
      return false;
    });

    if (internalConflictIndex !== -1) {
      return {
        hasConflict: true,
        isInternal: true,
        conflictIndex: internalConflictIndex
      };
    }

    // Check for facility conflicts with other teams
    if (!schedule.facilityId) {
      return { hasConflict: false };
    }

    const facilityConflictResult = checkScheduleConflict(
      facilitySchedules,
      schedule.facilityId,
      schedule.dayOfWeek,
      schedule.startTime,
      schedule.endTime,
      teamId
    );
    
    return {
      hasConflict: facilityConflictResult.hasConflict,
      isInternal: false,
      conflictingTeam: facilityConflictResult.conflictingTeam
    };
  };

  const addTrainingSchedule = () => {
    const newSchedules = [
      ...trainingSchedules,
      { dayOfWeek: 1, startTime: "09:00", endTime: "10:00", facilityId: "" }
    ];
    setTrainingSchedules(newSchedules);
    // Select the new schedule
    setSelectedScheduleIndex(newSchedules.length - 1);
  };

  const removeTrainingSchedule = (index: number) => {
    const newSchedules = trainingSchedules.filter((_, i) => i !== index);
    setTrainingSchedules(newSchedules);
    
    // Adjust selected index if necessary
    if (selectedScheduleIndex >= newSchedules.length) {
      setSelectedScheduleIndex(Math.max(0, newSchedules.length - 1));
    } else if (selectedScheduleIndex > index) {
      setSelectedScheduleIndex(selectedScheduleIndex - 1);
    }
  };

  const updateTrainingSchedule = (index: number, field: keyof TrainingScheduleForm, value: string | number) => {
    const updated = [...trainingSchedules];
    updated[index] = { ...updated[index], [field]: value };
    setTrainingSchedules(updated);
    
    // Auto-select the schedule being edited
    if (selectedScheduleIndex !== index) {
      setSelectedScheduleIndex(index);
    }
    
    // Update selected facility for availability based on the selected schedule
    if (field === "facilityId" && typeof value === "string") {
      setSelectedFacilityForAvailability(value);
    } else if (selectedScheduleIndex === index && updated[index].facilityId) {
      setSelectedFacilityForAvailability(updated[index].facilityId);
    }
  };

  const checkForConflicts = () => {
    return trainingSchedules.some((schedule, index) => hasConflict(schedule, index));
  };

  const getSelectedFacilitySchedule = () => {
    return trainingSchedules[selectedScheduleIndex] || null;
  };

  const selectSchedule = (index: number) => {
    setSelectedScheduleIndex(index);
    const schedule = trainingSchedules[index];
    if (schedule?.facilityId) {
      setSelectedFacilityForAvailability(schedule.facilityId);
    }
  };

  // Update facility availability when selected schedule changes
  useEffect(() => {
    const selectedSchedule = trainingSchedules[selectedScheduleIndex];
    if (selectedSchedule?.facilityId) {
      setSelectedFacilityForAvailability(selectedSchedule.facilityId);
    } else {
      setSelectedFacilityForAvailability("");
    }
  }, [selectedScheduleIndex, trainingSchedules]);

  return {
    trainingSchedules,
    facilitySchedules,
    selectedScheduleIndex,
    selectedFacilityForAvailability,
    hasConflict,
    getConflictInfo,
    addTrainingSchedule,
    removeTrainingSchedule,
    updateTrainingSchedule,
    checkForConflicts,
    getSelectedFacilitySchedule,
    selectSchedule,
    setSelectedFacilityForAvailability,
  };
}
