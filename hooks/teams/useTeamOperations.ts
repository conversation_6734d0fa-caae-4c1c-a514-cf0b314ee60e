import { useState } from "react";
import { useRouter } from "next/navigation";
import { updateTeamWithSchedules } from "@/lib/actions";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import type { TeamFormData } from "./useTeamForm";
import type { TrainingScheduleForm } from "./useScheduleManagement";
import {useToast} from "@/hooks/use-toast";

interface UseTeamOperationsProps {
  teamId: string;
}

export function useTeamOperations({ teamId }: UseTeamOperationsProps) {
  const router = useRouter();
  const { t } = useSafeTranslation();
  const { toast }= useToast();
  const [loading, setLoading] = useState(false);

  const updateTeam = async (
    formData: TeamFormData,
    schedules: TrainingScheduleForm[],
    hasConflicts: boolean
  ) => {
    try {
      setLoading(true);
      
      // Check for schedule conflicts
      if (hasConflicts) {
        toast({
          title: t("teams.messages.conflictError"),
          description: t("teams.messages.conflictErrorDetail"),
          variant: "destructive",
        });
        return false;
      }
      
      const teamData = {
        name: formData.name,
        description: formData.description,
        schoolId: formData.schoolId,
        instructorId: formData.instructorId,
        branchId: formData.branchId,
      };

      await updateTeamWithSchedules(teamId, teamData, schedules);
      toast({
        title: t('teams.messages.updateSuccess'),
        description: t('teams.messages.updateSuccessDetail'),
      })
      router.push(`/teams/${teamId}`);
      return true;
    } catch (err) {
      console.error('Error updating team:', err);
      toast({
        title: t('teams.messages.updateError'),
        description: t('teams.messages.updateErrorDetail'),
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  const navigateBack = () => {
    router.push(`/teams/${teamId}`);
  };

  return {
    loading,
    updateTeam,
    navigateBack,
  };
}
