import { useEffect, useState, useCallback } from 'react';

interface EntityCache {
  [key: string]: {
    name: string;
    timestamp: number;
  };
}

interface UseEntityNamesOptions {
  enabled?: boolean;
  cacheTimeout?: number; // in milliseconds
}

const CACHE_TIMEOUT = 5 * 60 * 1000; // 5 minutes default

export function useEntityNames(options: UseEntityNamesOptions = {}) {
  const [entityCache, setEntityCache] = useState<EntityCache>({});
  const [loading, setLoading] = useState(false);
  const cacheTimeout = options.cacheTimeout || CACHE_TIMEOUT;

  // Clear expired cache entries
  const clearExpiredCache = useCallback(() => {
    const now = Date.now();
    setEntityCache(prev => {
      const updated = { ...prev };
      let hasChanges = false;
      
      Object.keys(updated).forEach(key => {
        if (now - updated[key].timestamp > cacheTimeout) {
          delete updated[key];
          hasChanges = true;
        }
      });
      
      return hasChanges ? updated : prev;
    });
  }, [cacheTimeout]);

  // Clear expired cache entries every minute
  useEffect(() => {
    const interval = setInterval(clearExpiredCache, 60 * 1000);
    return () => clearInterval(interval);
  }, [clearExpiredCache]);

  const getEntityName = useCallback(async (entityType: string, entityId: string): Promise<string> => {
    if (!options.enabled) {
      return `${entityType} #${entityId.slice(0, 8)}`;
    }

    const cacheKey = `${entityType}-${entityId}`;
    const now = Date.now();
    
    // Return cached value if available and not expired
    if (entityCache[cacheKey] && (now - entityCache[cacheKey].timestamp) < cacheTimeout) {
      return entityCache[cacheKey].name;
    }

    try {
      setLoading(true);
      
      // Make actual API call to fetch entity name
      const response = await fetch(`/api/entity-name?type=${encodeURIComponent(entityType)}&id=${encodeURIComponent(entityId)}`);
      
      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      const entityName = data.entityName;

      if (!entityName) {
        throw new Error('Entity name not found in API response');
      }

      // Cache the result with timestamp
      setEntityCache(prev => ({
        ...prev,
        [cacheKey]: {
          name: entityName,
          timestamp: now
        }
      }));

      return entityName;
    } catch (error) {
      console.error(`Failed to fetch ${entityType} name:`, error);
      // Return fallback name
      const fallbackName = `${entityType.charAt(0).toUpperCase() + entityType.slice(1)} #${entityId.slice(0, 8)}`;
      
      // Cache the fallback to avoid repeated failed requests (shorter cache time for errors)
      setEntityCache(prev => ({
        ...prev,
        [cacheKey]: {
          name: fallbackName,
          timestamp: now
        }
      }));
      
      return fallbackName;
    } finally {
      setLoading(false);
    }
  }, [options.enabled, entityCache, cacheTimeout]);

  // Function to preload entity names (useful for performance optimization)
  const preloadEntityNames = useCallback(async (entities: Array<{ type: string, id: string }>) => {
    if (!options.enabled) return;

    const promises = entities.map(entity => 
      getEntityName(entity.type, entity.id).catch(error => {
        console.warn(`Failed to preload ${entity.type}:${entity.id}`, error);
        return null;
      })
    );

    await Promise.allSettled(promises);
  }, [getEntityName, options.enabled]);

  // Function to invalidate cache for specific entity
  const invalidateEntity = useCallback((entityType: string, entityId: string) => {
    const cacheKey = `${entityType}-${entityId}`;
    setEntityCache(prev => {
      const updated = { ...prev };
      delete updated[cacheKey];
      return updated;
    });
  }, []);

  // Function to clear all cache
  const clearCache = useCallback(() => {
    setEntityCache({});
  }, []);

  return {
    getEntityName,
    entityCache: Object.fromEntries(
      Object.entries(entityCache).map(([key, value]) => [key, value.name])
    ),
    loading,
    preloadEntityNames,
    invalidateEntity,
    clearCache,
    cacheSize: Object.keys(entityCache).length,
  };
}
