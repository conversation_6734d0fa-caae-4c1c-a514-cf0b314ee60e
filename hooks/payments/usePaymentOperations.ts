import { useState } from "react";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { updatePayment } from "@/lib/actions/payments";
import type { PaymentFormData } from "./usePaymentForm";

interface UsePaymentOperationsProps {
  paymentId: string;
}

export function usePaymentOperations({ paymentId }: UsePaymentOperationsProps) {
  const router = useRouter();
  const { toast } = useToast();
  const { t } = useSafeTranslation();
  const [isSaving, setIsSaving] = useState(false);

  const savePayment = async (formData: PaymentFormData): Promise<boolean> => {
    setIsSaving(true);
    try {
      await updatePayment(paymentId, {
        athleteId: formData.athleteId,
        amount: formData.amount,
        date: formData.date,
        dueDate: formData.dueDate,
        status: formData.status,
        type: formData.type,
        method: formData.method === "" ? null : formData.method as "cash" | "bank_transfer" | "credit_card",
        description: formData.description || null,
      });

      toast({
        title: t("payments.messages.updateSuccess"),
        description: t("payments.messages.updateSuccessDescription"),
      });

      return true;
    } catch (error) {
      console.error("Error updating payment:", error);
      toast({
        title: t("payments.messages.updateError"),
        description: t("payments.messages.updateErrorDescription"),
        variant: "destructive",
      });
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  const navigateBack = () => {
    router.push(`/payments/${paymentId}`);
  };

  const navigateToList = () => {
    router.push('/payments');
  };

  return {
    isSaving,
    savePayment,
    navigateBack,
    navigateToList,
  };
}
