"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { Users } from "lucide-react";
import { useTranslation } from "react-i18next";
import { signIn, useSession } from "next-auth/react";

export default function SignInPage() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { t } = useTranslation(['shared', 'auth']);
  const { data: session, status } = useSession();

  useEffect(() => {
    if (session) {
      router.push("/dashboard");
    }
  }, [session, router]);

  const handleSignIn = async () => {
    setIsLoading(true);
    try {
      await signIn("zitadel", { callbackUrl: "/dashboard" });
    } catch (error) {
      console.error("Sign in error:", error);
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900 p-4">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md w-full bg-white dark:bg-slate-800 rounded-xl shadow-lg p-8"
      >
        <div className="flex justify-center mb-8">
          <div className="relative">
            <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500 to-orange-500 rounded-full blur opacity-75"></div>
            <div className="relative bg-white dark:bg-slate-800 rounded-full p-4">
              <div className="flex items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-orange-500 w-16 h-16">
                <Users className="h-8 w-8 text-white" />
              </div>
            </div>
          </div>
        </div>
        
        <h1 className="text-2xl font-bold text-center mb-2">Sports Academy Management</h1>
        <p className="text-center text-muted-foreground mb-8">{t('auth.welcomeMessage', { ns: 'auth' })}</p>
        
        <Button 
          className="w-full mb-4" 
          size="lg" 
          onClick={handleSignIn} 
          disabled={isLoading}
        >
          {isLoading ? t('common.loading', { ns: 'shared' }) : t('auth.signIn', { ns: 'auth' })}
        </Button>
        
        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            {t('auth.noAccount', { ns: 'auth' })}{" "}
            <Link href="/auth/signup" className="text-primary hover:underline">
              {t('auth.signUp', { ns: 'auth' })}
            </Link>
          </p>
        </div>
      </motion.div>
    </div>
  );
}