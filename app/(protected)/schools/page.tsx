import React, { Suspense } from "react";
import { getSchools } from "@/lib/actions";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON>, <PERSON><PERSON>ontent, CardFooter, CardHeader } from "@/components/ui/card";
import { SchoolsPageClient } from "./schools-page-client";

async function SchoolList() {
  const schools = await getSchools();
  return <SchoolsPageClient schools={schools} />;
}

function SchoolListSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-32" />
        <Skeleton className="h-10 w-32" />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1, 2, 3, 4, 5].map((i) => (
          <Card key={i} className="overflow-hidden">
            <Skeleton className="h-48 w-full" />
            <CardHeader>
              <Skeleton className="h-6 w-3/4 mb-2" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2 mb-4">
                <Skeleton className="h-6 w-16 rounded-full" />
                <Skeleton className="h-6 w-16 rounded-full" />
              </div>
              <Skeleton className="h-4 w-1/3" />
            </CardContent>
            <CardFooter className="flex justify-between">
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-24" />
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}

export default function SchoolsPage() {
  return (
    <Suspense fallback={<SchoolListSkeleton />}>
      <SchoolList />
    </Suspense>
  );
}
