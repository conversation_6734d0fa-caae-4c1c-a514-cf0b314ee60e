"use client";

import React from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { School } from "@/lib/types";
import { SchoolListClient } from "./school-list-client";

interface SchoolsPageClientProps {
  schools: School[];
}

export function SchoolsPageClient({ schools }: SchoolsPageClientProps) {
  const { t } = useSafeTranslation();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold tracking-tight">{t('schools.title', { ns: 'schools' })}</h1>
        <Button asChild>
          <Link href="/schools/new">
            <PlusCircle className="mr-2 h-4 w-4" />
            {t('schools.actions.addNew', { ns: 'schools' })}
          </Link>
        </Button>
      </div>
      <SchoolListClient schools={schools} />
    </div>
  );
}
