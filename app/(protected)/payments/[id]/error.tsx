"use client";

import { useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertTriangle, ChevronLeft, RefreshCw } from "lucide-react";
import { useRouter } from "next/navigation";
import { useSafeTranslation } from "@/hooks/use-safe-translation";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const router = useRouter();
  const { t } = useSafeTranslation();

  useEffect(() => {
    console.error("Payment detail error:", error);
  }, [error]);

  const handleBack = () => {
    router.push("/payments");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" onClick={handleBack}>
          <ChevronLeft className="mr-2 h-4 w-4" />
          {t('common.actions.back')}
        </Button>
        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            {t('payments.details.title')}
          </h1>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            {t('common.error')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">
            {t('common.errorMessages.loadingFailed')}
          </p>
          
          {process.env.NODE_ENV === 'development' && (
            <details className="mt-4">
              <summary className="cursor-pointer text-sm font-medium">
                Technical Details
              </summary>
              <pre className="mt-2 p-4 bg-muted rounded-md text-xs overflow-auto">
                {error.message}
              </pre>
            </details>
          )}

          <div className="flex gap-2 pt-4">
            <Button onClick={reset} className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              {t('common.actions.retry')}
            </Button>
            <Button variant="outline" onClick={handleBack}>
              {t('common.actions.back')}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
