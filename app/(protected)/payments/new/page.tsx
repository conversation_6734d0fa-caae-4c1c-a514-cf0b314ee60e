import { Metadata } from "next";
import { notFound } from "next/navigation";
import { getAthletes } from "@/lib/actions";
import AddPaymentClient from "./add-payment-client";

export const metadata: Metadata = {
  title: "Add New Payment | Sports Club Management",
  description: "Create a new payment record for athletes",
};

export default async function AddPaymentPage() {
  try {
    const athletes = await getAthletes();

    return (
      <AddPaymentClient 
        athletes={athletes}
      />
    );
  } catch (error) {
    console.error("Error loading add payment page:", error);
    notFound();
  }
}