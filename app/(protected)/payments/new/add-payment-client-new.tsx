"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  ChevronLeft,
  Save,
  Calendar,
  User,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import { TurkishLiraIcon } from "@/components/ui/turkish-lira-icon";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { useToast } from "@/hooks/use-toast";
import { createPayment } from "@/lib/actions/payments";
import { DatePicker } from "@/components/ui/date-picker";
import { formatDateToLocalString } from "@/lib/utils/date-formatter";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Al<PERSON>, AlertDescription } from "@/components/ui/alert";

interface AddPaymentProps {
  athletes: Array<{
    id: string;
    name: string;
    surname: string;
    parentEmail?: string | null;
    parentPhone?: string | null;
    nationalId?: string | null;
  }>;
}

export default function AddPaymentClient({ athletes }: AddPaymentProps) {
  const router = useRouter();
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState<{
    athleteId: string;
    amount: string;
    date: string;
    dueDate: string;
    status: "pending" | "completed" | "overdue";
    type: "fee" | "equipment" | "other";
    method: string;
    description: string;
  }>({
    athleteId: "",
    amount: "",
    date: new Date().toISOString().split('T')[0],
    dueDate: new Date().toISOString().split('T')[0],
    status: "pending",
    type: "fee",
    method: "none",
    description: "",
  });

  const selectedAthlete = athletes.find(a => a.id === formData.athleteId);

  const handleBack = () => {
    router.push("/payments");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.athleteId || !formData.amount || !formData.date || !formData.dueDate) {
      toast({
        title: t('common.error'),
        description: t('payments.validation.required'),
        variant: "destructive",
      });
      return;
    }

    // Validate due date is not before date
    if (new Date(formData.dueDate) < new Date(formData.date)) {
      toast({
        title: t('common.error'),
        description: t('payments.validation.dueDateBeforeDate'),
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSaving(true);
      
      const result = await createPayment({
        athleteId: formData.athleteId,
        amount: formData.amount,
        date: formData.date,
        dueDate: formData.dueDate,
        status: formData.status,
        type: formData.type,
        method: formData.method === "" || formData.method === "none" ? null : formData.method as "cash" | "bank_transfer" | "credit_card",
        description: formData.description || null,
      });

      toast({
        title: t('payments.messages.createSuccess'),
        description: t('payments.messages.createSuccessDetail'),
        variant: "default",
      });
      
      router.push("/payments");
    } catch (error) {
      console.error("Error creating payment:", error);
      toast({
        title: t('payments.messages.createError'),
        description: t('payments.messages.createErrorDetail'),
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const formatAmount = (amount: string) => {
    return new Intl.NumberFormat('tr-TR', { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    }).format(parseFloat(amount));
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={handleBack}>
            <ChevronLeft className="mr-2 h-4 w-4" />
            {t('common.actions.back')}
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">
              {t('payments.addPage.title')}
            </h1>
            <p className="text-muted-foreground">
              {t('payments.addPage.description')}
            </p>
          </div>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Form */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TurkishLiraIcon className="h-5 w-5" />
                {t('payments.addPage.details')}
              </CardTitle>
              <CardDescription>
                {t('payments.addPage.formDescription')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="athlete" className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      {t('athletes.title')} *
                    </Label>
                    <Select
                      value={formData.athleteId}
                      onValueChange={(value) =>
                        setFormData((prev) => ({ ...prev, athleteId: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={t('athletes.select')} />
                      </SelectTrigger>
                      <SelectContent>
                        {athletes.map((athlete) => (
                          <SelectItem key={athlete.id} value={athlete.id}>
                            <div className="flex flex-col items-start">
                              <span className="font-medium">
                                {athlete.name} {athlete.surname}
                              </span>
                              {athlete.nationalId && (
                                <span className="text-xs text-muted-foreground">
                                  ID: {athlete.nationalId}
                                </span>
                              )}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="amount" className="flex items-center gap-2">
                      <TurkishLiraIcon className="h-4 w-4" />
                      {t('payments.details.amount')} *
                    </Label>
                    <Input
                      id="amount"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.amount}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, amount: e.target.value }))
                      }
                      placeholder="0.00"
                      className="font-mono"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      {t('payments.details.date')} *
                    </Label>
                    <DatePicker
                      date={formData.date ? new Date(formData.date) : undefined}
                      onSelect={(date) => {
                        if (date) {
                          setFormData((prev) => ({ ...prev, date: formatDateToLocalString(date) }));
                        } else {
                          setFormData((prev) => ({ ...prev, date: '' }));
                        }
                      }}
                      placeholder={t('payments.placeholders.selectDate')}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      {t('payments.details.dueDate')} *
                    </Label>
                    <DatePicker
                      date={formData.dueDate ? new Date(formData.dueDate) : undefined}
                      onSelect={(date) =>
                        setFormData((prev) => ({
                          ...prev,
                          dueDate: date ? formatDateToLocalString(date) : ''
                        }))
                      }
                      placeholder={t('payments.placeholders.selectDueDate')}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="type">
                      {t('payments.details.type')} *
                    </Label>
                    <Select
                      value={formData.type}
                      onValueChange={(value: "fee" | "equipment" | "other") =>
                        setFormData((prev) => ({ ...prev, type: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={t('payments.selectType')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="fee">{t('payments.types.fee')}</SelectItem>
                        <SelectItem value="equipment">{t('payments.types.equipment')}</SelectItem>
                        <SelectItem value="other">{t('payments.types.other')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="method">
                      {t('payments.details.method')} *
                    </Label>
                    <Select
                      value={formData.method || "none"}
                      onValueChange={(value: string) =>
                        setFormData((prev) => ({ ...prev, method: value === "none" ? "" : value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={t('payments.selectMethod')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">{t('payments.methods.not_specified')}</SelectItem>
                        <SelectItem value="cash">{t('payments.methods.cash')}</SelectItem>
                        <SelectItem value="bank_transfer">{t('payments.methods.bank_transfer')}</SelectItem>
                        <SelectItem value="credit_card">{t('payments.methods.credit_card')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="status">
                      {t('payments.details.status')} *
                    </Label>
                    <Select
                      value={formData.status}
                      onValueChange={(value: "pending" | "completed" | "overdue") =>
                        setFormData((prev) => ({
                          ...prev,
                          status: value,
                          // Reset payment method if status is not completed
                          method: value === "completed" ? prev.method : "none"
                        }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={t('payments.selectStatus')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pending">
                          <div className="flex items-center gap-2">
                            <AlertTriangle className="h-4 w-4 text-yellow-600" />
                            {t('payments.status.pending')}
                          </div>
                        </SelectItem>
                        <SelectItem value="completed">
                          <div className="flex items-center gap-2">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            {t('payments.status.completed')}
                          </div>
                        </SelectItem>
                        <SelectItem value="overdue">
                          <div className="flex items-center gap-2">
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                            {t('payments.status.overdue')}
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">
                    {t('payments.details.description')}
                  </Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, description: e.target.value }))
                    }
                    placeholder={t('payments.addPage.descriptionPlaceholder')}
                    rows={3}
                  />
                </div>

                {formData.dueDate && formData.date && new Date(formData.dueDate) < new Date(formData.date) && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      {t('payments.validation.dueDateBeforeDate')}
                    </AlertDescription>
                  </Alert>
                )}

                <div className="flex justify-end gap-4 pt-4 border-t">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleBack}
                    disabled={isSaving}
                  >
                    {t('common.actions.cancel')}
                  </Button>
                  <Button type="submit" disabled={isSaving}>
                    <Save className="mr-2 h-4 w-4" />
                    {isSaving ? t('common.actions.saving') : t('common.actions.save')}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar Information */}
        <div className="space-y-6">
          {/* Selected Athlete Info */}
          {selectedAthlete && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <User className="h-4 w-4" />
                  {t('athletes.selectedAthlete')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="font-medium">
                    {selectedAthlete.name} {selectedAthlete.surname}
                  </p>
                  {selectedAthlete.nationalId && (
                    <p className="text-sm text-muted-foreground">
                      ID: {selectedAthlete.nationalId}
                    </p>
                  )}
                </div>
                {selectedAthlete.parentEmail && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {t('athletes.details.parentEmail')}
                    </p>
                    <p className="text-sm break-all">{selectedAthlete.parentEmail}</p>
                  </div>
                )}
                {selectedAthlete.parentPhone && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {t('athletes.details.parentPhone')}
                    </p>
                    <p className="text-sm">{selectedAthlete.parentPhone}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Payment Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">
                {t('payments.addPage.info')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {t('payments.addPage.manualPaymentNote')}
                </AlertDescription>
              </Alert>
              <div className="text-sm text-muted-foreground">
                <p>
                  {t('payments.addPage.paymentPlanNote')}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
