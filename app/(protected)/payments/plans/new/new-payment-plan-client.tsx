"use client";

import { useState, useTransition } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ArrowLeft, Save, Calendar, Building2, FileText } from "lucide-react";
import { TurkishLiraIcon } from "@/components/ui/turkish-lira-icon";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { createPaymentPlan } from "@/lib/actions";
import {useToast} from "@/hooks/use-toast";

interface Branch {
  id: string;
  name: string;
  description?: string | null;
}

interface NewPaymentPlanClientProps {
  branches: Branch[];
}

export default function NewPaymentPlanClient({ branches }: NewPaymentPlanClientProps) {
  const router = useRouter();
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [isPending, startTransition] = useTransition();
  
  const [formData, setFormData] = useState({
    name: "",
    amount: "",
    assignDay: "1",
    dueDay: "1",
    status: "active" as "active" | "inactive",
    description: "",
    selectedBranches: [] as string[],
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.name || !formData.amount || !formData.assignDay || !formData.dueDay) {
      toast({
        title: t('common.error'),
        description: t('payments.planCreate.messages.validationError'),
        variant: 'destructive',
      });
      return;
    }

    if (formData.selectedBranches.length === 0) {
      toast({
        title: t('common.error'),
        description: t('payments.planCreate.validation.branchesRequired'),
        variant: 'destructive',
      });
      return;
    }

    const assignDay = parseInt(formData.assignDay);
    const dueDay = parseInt(formData.dueDay);
    if (assignDay < 1 || assignDay > 31 || dueDay < 1 || dueDay > 31) {
      toast({
        title: t('common.error'),
        description: t('payments.planCreate.validation.invalidDays'),
        variant: 'destructive',
      });
      return;
    }

    startTransition(async () => {
      try {
        const result = await createPaymentPlan({
          name: formData.name,
          monthlyValue: parseFloat(formData.amount).toString(),
          assignDay: parseInt(formData.assignDay),
          dueDay: parseInt(formData.dueDay),
          status: formData.status as "active" | "inactive",
          description: formData.description || undefined,
          selectedBranches: formData.selectedBranches,
        });

        if (result.success) {
          toast({
            title: t('common.success'),
            description: t('payments.planCreate.messages.createSuccess'),
          });
          router.push(`/payments/plans/${result.data.id}`);
          router.refresh();
        } else {
          console.error("Failed to create payment plan");
          let errorDescriptionKey = '';
          if(result.errorType == 'BusinessRuleError'){
            errorDescriptionKey = `errors.${result.error}`;
          }else{
            errorDescriptionKey = 'payments.planCreate.messages.createError';
          }
          toast({
            title: t('common.error'),
            description: t(errorDescriptionKey),
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error("Error creating payment plan:", error);
        toast({
          title: t('common.error'),
          description: t('payments.planCreate.messages.createError'),
          variant: 'destructive',
        });
      }
    });
  };

  const handleInputChange = (field: string, value: string | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleBranchToggle = (branchId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      selectedBranches: checked
        ? [...prev.selectedBranches, branchId]
        : prev.selectedBranches.filter(id => id !== branchId)
    }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('common.actions.back')}
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('payments.planCreate.title')}</h1>
            <p className="text-muted-foreground">{t('payments.planCreate.subtitle')}</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Left Column - Basic Information */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  {t('payments.planCreate.basicInfo.title')}
                </CardTitle>
                <CardDescription>
                  {t('payments.planCreate.basicInfo.subtitle')}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">{t('payments.planCreate.fields.name')} *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder={t('payments.planCreate.placeholders.name')}
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="amount">{t('payments.planCreate.fields.amount')} *</Label>
                    <Input
                      id="amount"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.amount}
                      onChange={(e) => handleInputChange("amount", e.target.value)}
                      placeholder={t('payments.planCreate.placeholders.amount')}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="assignDay">{t('payments.planCreate.fields.assignDay')} *</Label>
                    <Input
                      id="assignDay"
                      type="number"
                      min="1"
                      max="31"
                      value={formData.assignDay}
                      onChange={(e) => handleInputChange("assignDay", e.target.value)}
                      placeholder="1"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="dueDay">{t('payments.planCreate.fields.dueDay')} *</Label>
                    <Input
                      id="dueDay"
                      type="number"
                      min="1"
                      max="31"
                      value={formData.dueDay}
                      onChange={(e) => handleInputChange("dueDay", e.target.value)}
                      placeholder="1"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="status">{t('payments.planCreate.fields.status')} *</Label>
                    <Select
                      value={formData.status}
                      onValueChange={(value: "active" | "inactive") => handleInputChange("status", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={t('payments.planCreate.placeholders.selectStatus')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">{t('common.status.active')}</SelectItem>
                        <SelectItem value="inactive">{t('common.status.inactive')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">{t('payments.planCreate.fields.description')}</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange("description", e.target.value)}
                    placeholder={t('payments.planCreate.placeholders.description')}
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Branch Selection */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  {t('payments.planCreate.branches.title')}
                </CardTitle>
                <CardDescription>
                  {t('payments.planCreate.branches.subtitle')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {branches.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    {t('payments.planCreate.branches.loading')}
                  </div>
                ) : (
                  <div className="space-y-3">
                    {branches.map((branch) => (
                      <div key={branch.id} className="flex items-start space-x-3 p-3 rounded-lg border">
                        <Checkbox
                          id={`branch-${branch.id}`}
                          checked={formData.selectedBranches.includes(branch.id)}
                          onCheckedChange={(checked) => handleBranchToggle(branch.id, checked as boolean)}
                        />
                        <div className="flex-1 min-w-0">
                          <Label 
                            htmlFor={`branch-${branch.id}`} 
                            className="text-sm font-medium cursor-pointer"
                          >
                            {t(`common.branches.${branch.name}`, { ns: 'shared' })}
                          </Label>
                          {branch.description && (
                            <p className="text-xs text-muted-foreground mt-1">
                              {branch.description}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                    
                    {formData.selectedBranches.length === 0 && (
                      <Alert>
                        <AlertDescription>
                          {t('payments.planCreate.branches.selectAtLeastOne')}
                        </AlertDescription>
                      </Alert>
                    )}
                    
                    {formData.selectedBranches.length > 0 && (
                      <div className="mt-4 p-3 bg-muted/50 rounded-lg">
                        <p className="text-sm text-muted-foreground">
                          {t('payments.planCreate.branches.selected', { 
                            selected: formData.selectedBranches.length, 
                            total: branches.length 
                          })}
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Preview Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TurkishLiraIcon className="h-5 w-5" />
                  {t('payments.planCreate.summary.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">{t('payments.planCreate.summary.amount')}:</span>
                  <span className="font-medium">
                    {new Intl.NumberFormat('tr-TR', {
                      style: 'currency',
                      currency: 'TRY'
                    }).format(parseFloat(formData.amount) || 0)}
                  </span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">{t('payments.planCreate.summary.assignDay')}:</span>
                  <span className="font-medium">{formData.assignDay}th of each month</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">{t('payments.planCreate.summary.dueDay')}:</span>
                  <span className="font-medium">{formData.dueDay}th of each month</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">{t('payments.planCreate.summary.status')}:</span>
                  <Badge variant="outline" className="capitalize">
                    {formData.status}
                  </Badge>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">{t('payments.planCreate.summary.branches')}:</span>
                  <span className="font-medium">{formData.selectedBranches.length}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 pt-6 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isPending}
          >
            {t('payments.planCreate.buttons.cancel')}
          </Button>
          <Button 
            type="submit" 
            disabled={isPending || formData.selectedBranches.length === 0}
            className="min-w-[120px]"
          >
            {isPending ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                {t('payments.planCreate.buttons.saving')}
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                {t('payments.planCreate.buttons.save')}
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
