import { notFound } from "next/navigation";
import { getPaymentPlanById } from "@/lib/actions";
import PaymentPlanEditForm from "./payment-plan-edit-form";

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function EditPaymentPlanPage({ params }: PageProps) {
  const { id } = await params;
  const paymentPlan = await getPaymentPlanById(id);

  if (!paymentPlan) {
    notFound();
  }

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      <PaymentPlanEditForm paymentPlan={paymentPlan} />
    </div>
  );
}