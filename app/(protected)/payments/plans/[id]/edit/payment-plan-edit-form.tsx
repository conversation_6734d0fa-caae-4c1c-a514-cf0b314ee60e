"use client";

import { useState, useTransition } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ArrowLeft, Save, Calendar, Building2, FileText } from "lucide-react";
import { TurkishLiraIcon } from "@/components/ui/turkish-lira-icon";
import { PaymentPlan } from "@/lib/types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { updatePaymentPlan, validatePaymentPlanData } from "@/lib/actions";
import { getBranches } from "@/lib/actions";
import { useEffect } from "react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { useToast } from "@/hooks/use-toast";

interface PaymentPlanEditFormProps {
  paymentPlan: PaymentPlan;
}

export default function PaymentPlanEditForm({ paymentPlan }: PaymentPlanEditFormProps) {
  const router = useRouter();
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [isPending, startTransition] = useTransition();
  const [branches, setBranches] = useState<Array<{ id: string; name: string; description?: string | null }>>([]);
  const [formData, setFormData] = useState({
    name: paymentPlan.name,
    monthlyValue: paymentPlan.monthlyValue || "",
    assignDay: paymentPlan.assignDay?.toString() || "1",
    dueDay: paymentPlan.dueDay?.toString() || "1", 
    status: paymentPlan.status as "active" | "inactive",
    description: paymentPlan.description || "",
    selectedBranches: paymentPlan.branches?.map(b => b.id).filter(Boolean) as string[] || [],
  });

  useEffect(() => {
    const fetchBranches = async () => {
      try {
        const branchData = await getBranches();
        setBranches(branchData);
      } catch (error) {
        console.error("Error fetching branches:", error);
        toast({
          title: t('common.error'),
          description: t('payments.planEdit.errors.failedToLoadBranches'),
          variant: 'destructive',
        });
      }
    };

    void fetchBranches();
  }, [t, toast]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Transform form data to match the expected types
    const transformedData = {
      ...formData,
      assignDay: parseInt(formData.assignDay),
      dueDay: parseInt(formData.dueDay),
    };

    // Validate the form data
    const validation = await validatePaymentPlanData(transformedData);
    if (!validation.isValid) {
      validation.errors.forEach(error =>
          toast({
            title: t('common.error'),
            description: error,
            variant: 'destructive',
          })
      );
      return;
    }

    startTransition(async () => {
      try {
        const result = await updatePaymentPlan(paymentPlan.id, transformedData);
        
        if (result.success) {
          toast({
            title: t('common.success'),
            description: t('payments.planEdit.messages.updateSuccess')
          })
          router.push(`/payments/plans/${paymentPlan.id}`);
          router.refresh();
        } else {
          console.error("Error updating payment plan");
          let errorDescriptionKey = '';
          if(result.errorType == 'BusinessRuleError'){
            errorDescriptionKey = `errors.${result.error}`;
          }else{
            errorDescriptionKey = 'payments.planEdit.errors.updateFailed';
          }
          toast({
            title: t('common.error'),
            description: t(errorDescriptionKey),
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error("Error updating payment plan:", error);
        toast({
          title: t('common.error'),
          description: t('payments.planEdit.errors.updateFailed'),
          variant: 'destructive',
        });
      }
    });
  };

  const handleInputChange = (field: string, value: string | string[]) => {
    setFormData({ ...formData, [field]: value });
  };

  const handleBranchToggle = (branchId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      selectedBranches: checked
        ? [...prev.selectedBranches, branchId]
        : prev.selectedBranches.filter(id => id !== branchId)
    }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('common.actions.back')}
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('payments.planEdit.title')}</h1>
            <p className="text-muted-foreground">{t('payments.planEdit.subtitle')}</p>
          </div>
        </div>
        <Badge variant="outline" className="text-sm">
          {t(`payments.planEdit.status.${paymentPlan.status}`)} • {t('payments.planEdit.branchesCount', { count: paymentPlan.branches?.length || 0 })}
        </Badge>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Left Column - Basic Information */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  {t('payments.planEdit.basicInfo.title')}
                </CardTitle>
                <CardDescription>
                  {t('payments.planEdit.basicInfo.subtitle')}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">{t('payments.planEdit.fields.name')} *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="Enter plan name"
                    required
                  />
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="monthlyValue">{t('payments.planEdit.fields.monthlyValue')} *</Label>
                    <Input
                      id="monthlyValue"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.monthlyValue}
                      onChange={(e) => handleInputChange("monthlyValue", e.target.value)}
                      placeholder="0.00"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="assignDay">{t('payments.planEdit.fields.assignDay')} *</Label>
                    <Input
                      id="assignDay"
                      type="number"
                      min="1"
                      max="31"
                      value={formData.assignDay}
                      onChange={(e) => handleInputChange("assignDay", e.target.value)}
                      placeholder="1"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="dueDay">{t('payments.planEdit.fields.dueDay')} *</Label>
                    <Input
                      id="dueDay"
                      type="number"
                      min="1"
                      max="31"
                      value={formData.dueDay}
                      onChange={(e) => handleInputChange("dueDay", e.target.value)}
                      placeholder="1"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">{t('payments.planEdit.fields.status')} *</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value: "active" | "inactive") => handleInputChange("status", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t('payments.planEdit.placeholders.selectStatus')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">{t('payments.planEdit.status.active')}</SelectItem>
                      <SelectItem value="inactive">{t('payments.planEdit.status.inactive')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">{t('payments.planEdit.fields.description')}</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange("description", e.target.value)}
                    placeholder={t('payments.planEdit.placeholders.description')}
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Branch Selection */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  {t('payments.planEdit.branches.title')}
                </CardTitle>
                <CardDescription>
                  {t('payments.planEdit.branches.subtitle')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {branches.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    {t('payments.planEdit.branches.loading')}
                  </div>
                ) : (
                  <div className="space-y-3">
                    {branches.map((branch) => (
                      <div key={branch.id} className="flex items-start space-x-3 p-3 rounded-lg border">
                        <Checkbox
                          id={`branch-${branch.id}`}
                          checked={formData.selectedBranches.includes(branch.id)}
                          onCheckedChange={(checked) => handleBranchToggle(branch.id, checked as boolean)}
                        />
                        <div className="flex-1 min-w-0">
                          <Label 
                            htmlFor={`branch-${branch.id}`} 
                            className="text-sm font-medium cursor-pointer"
                          >
                            {t(`common.branches.${branch.name}`, { ns: 'shared' })}
                          </Label>
                          {branch.description && (
                            <p className="text-xs text-muted-foreground mt-1">
                              {branch.description}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}

                    {formData.selectedBranches.length === 0 && (
                      <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                        <p className="text-sm text-amber-800">
                          {t('payments.planEdit.branches.selectAtLeastOne')}
                        </p>
                      </div>
                    )}

                    {formData.selectedBranches.length > 0 && (
                      <div className="mt-4 p-3 bg-muted/50 rounded-lg">
                        <p className="text-sm text-muted-foreground">
                          {t('payments.planEdit.branches.selected', { 
                            selected: formData.selectedBranches.length, 
                            total: branches.length 
                          })}
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Preview Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TurkishLiraIcon className="h-5 w-5" />
                  {t('payments.planEdit.summary.title')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">{t('payments.planEdit.summary.monthlyValue')}:</span>
                  <span className="font-medium">
                    {new Intl.NumberFormat('tr-TR', {
                      style: 'currency',
                      currency: 'TRY'
                    }).format(parseFloat(formData.monthlyValue) || 0)}
                  </span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">{t('payments.planEdit.summary.assignDay')}:</span>
                  <span className="font-medium">
                    {t('payments.plans.dayOfMonth', { day: formData.assignDay })}
                  </span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">{t('payments.planEdit.summary.dueDay')}:</span>
                  <span className="font-medium">
                    {t('payments.plans.dayOfMonth', { day: formData.dueDay })}
                  </span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">{t('payments.planEdit.summary.status')}:</span>
                  <Badge variant="outline" className="capitalize">
                    {t(`payments.planEdit.status.${formData.status}`)}
                  </Badge>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">{t('payments.planEdit.summary.branches')}:</span>
                  <span className="font-medium">{formData.selectedBranches.length}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 pt-6 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isPending}
          >
            {t('common.actions.cancel')}
          </Button>
          <Button 
            type="submit" 
            disabled={isPending || formData.selectedBranches.length === 0}
            className="min-w-[120px]"
          >
            {isPending ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                {t('payments.planEdit.buttons.saving')}
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                {t('payments.planEdit.buttons.save')}
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
