"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { Card, CardContent } from "@/components/ui/card";
import { PlusCircle, Filter } from "lucide-react";
import Link from "next/link";
import { Expense } from "@/lib/types";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { useExpenseColumns } from "@/components/expenses/expenses-table-columns";
import { GenericListPage } from "@/components/ui/generic-list-page";
import { PaginationData, SearchParams } from "@/hooks/use-paginated-list";

interface ExpensesListPaginatedProps {
  initialData: Expense[];
  initialPagination: PaginationData;
  initialSearchParams: SearchParams & {
    category?: string;
    fromDate?: string;
    toDate?: string;
    instructorId?: string;
    facilityId?: string;
  };
}

export function ExpensesListPaginated({
  initialData,
  initialPagination,
  initialSearchParams
}: ExpensesListPaginatedProps) {
  const { t } = useSafeTranslation();
  const router = useRouter();
  const columns = useExpenseColumns();

  // Local state for filters
  const [filters, setFilters] = useState({
    category: initialSearchParams.category || "",
    fromDate: initialSearchParams.fromDate || "",
    toDate: initialSearchParams.toDate || "",
    instructorId: initialSearchParams.instructorId || "",
    facilityId: initialSearchParams.facilityId || "",
  });
  const [showFilters, setShowFilters] = useState(
    !!(initialSearchParams.category || initialSearchParams.fromDate ||
       initialSearchParams.toDate || initialSearchParams.instructorId ||
       initialSearchParams.facilityId)
  );

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleApplyFilters = () => {
    const searchParams = new URLSearchParams(window.location.search);

    // Update search params with filter values
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value.trim() !== '') {
        searchParams.set(key, value);
      } else {
        searchParams.delete(key);
      }
    });

    // Reset to page 1 when applying filters
    searchParams.set('page', '1');

    // Use router.push for smooth navigation without page refresh
    router.push(`/expenses?${searchParams.toString()}`);
  };

  const handleClearFilters = () => {
    // Reset all filter states
    setFilters({
      category: "",
      fromDate: "",
      toDate: "",
      instructorId: "",
      facilityId: "",
    });

    // Clear URL parameters
    const searchParams = new URLSearchParams(window.location.search);

    // Remove filter parameters but keep search and pagination
    ['category', 'fromDate', 'toDate', 'instructorId', 'facilityId'].forEach(key => {
      searchParams.delete(key);
    });

    // Reset to page 1
    searchParams.set('page', '1');

    // Use router.push for smooth navigation without page refresh
    router.push(`/expenses?${searchParams.toString()}`);
  };

  // Create actions for the header
  const actions = (
    <Button asChild>
      <Link href="/expenses/new">
        <PlusCircle className="mr-2 h-4 w-4" />
        {t('expenses.new')}
      </Link>
    </Button>
  );

  // Create filters component
  const filtersComponent = showFilters ? (
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">{t('common.actions.filter')}</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(false)}
            >
              {t('common.actions.cancel')}
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category">{t('expenses.details.category')}</Label>
              <Select value={filters.category || "all"} onValueChange={(value) => handleFilterChange('category', value === 'all' ? '' : value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('common.all')}</SelectItem>
                  <SelectItem value="salary">{t('expenses.categories.salary')}</SelectItem>
                  <SelectItem value="insurance">{t('expenses.categories.insurance')}</SelectItem>
                  <SelectItem value="rent">{t('expenses.categories.rent')}</SelectItem>
                  <SelectItem value="equipment">{t('expenses.categories.equipment')}</SelectItem>
                  <SelectItem value="other">{t('expenses.categories.other')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="fromDate">{t('expenses.filters.fromDate')}</Label>
              <DatePicker
                date={filters.fromDate ? new Date(filters.fromDate) : undefined}
                onSelect={(date) => handleFilterChange('fromDate', date ? date.toISOString().split('T')[0] : '')}
                placeholder={t('expenses.filters.selectFromDate')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="toDate">{t('expenses.filters.toDate')}</Label>
              <DatePicker
                date={filters.toDate ? new Date(filters.toDate) : undefined}
                onSelect={(date) => handleFilterChange('toDate', date ? date.toISOString().split('T')[0] : '')}
                placeholder={t('expenses.filters.selectToDate')}
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button onClick={handleApplyFilters}>
              {t('common.actions.apply')}
            </Button>
            <Button variant="outline" onClick={handleClearFilters}>
              {t('common.actions.clear')}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  ) : (
    <Button
      variant="outline"
      onClick={() => setShowFilters(true)}
    >
      <Filter className="mr-2 h-4 w-4" />
      {t('common.actions.filter')}
    </Button>
  );

  return (
    <GenericListPage
      data={initialData}
      pagination={initialPagination}
      columns={columns}
      title={t('expenses.title')}
      description="Manage your expenses and track spending"
      basePath="/expenses"
      initialSearchParams={initialSearchParams}
      actions={actions}
      filters={filtersComponent}
      searchPlaceholder={t('expenses.placeholders.searchExpenses')}
      paginationOptions={{
        defaultSortBy: 'date',
        defaultSortOrder: 'desc',
        searchMinLength: 3,
        searchDebounceMs: 500,
      }}
    />
  );
}
