import { notFound } from "next/navigation";
import { getExpenseById } from "@/lib/actions/expenses";
import ExpenseDetailClient from "./expense-detail-client";

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function ExpenseDetailPage({ params }: PageProps) {
  const { id } = await params;
  
  try {
    const expense = await getExpenseById(id);
    
    if (!expense) {
      notFound();
    }

    return <ExpenseDetailClient expense={expense} />;
  } catch (error) {
    console.error('Error fetching expense:', error);
    notFound();
  }
}
