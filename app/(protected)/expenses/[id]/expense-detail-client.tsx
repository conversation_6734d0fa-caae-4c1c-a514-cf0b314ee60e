"use client";

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Edit, Calendar, Tag, FileText, User, Building } from "lucide-react";
import { TurkishLiraIcon } from "@/components/ui/turkish-lira-icon";
import Link from "next/link";
import { format } from "date-fns";
import type { Expense } from "@/lib/types";
import { useSafeTranslation } from "@/hooks/use-safe-translation";

interface ExpenseDetailClientProps {
  expense: Expense;
}

export default function ExpenseDetailClient({ expense }: ExpenseDetailClientProps) {
  const { t } = useSafeTranslation();

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/expenses">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t('common.actions.back')}
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">{t('expenses.viewDetails')}</h1>
        </div>
        <Button asChild>
          <Link href={`/expenses/${expense.id}/edit`}>
            <Edit className="h-4 w-4 mr-2" />
            {t('expenses.actions.edit')}
          </Link>
        </Button>
      </div>

      {/* Expense Details Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TurkishLiraIcon className="h-5 w-5" />
            <span>{t('expenses.details.expenseDetails')}</span>
          </CardTitle>
          <CardDescription>
            {t('expenses.details.expenseDate')}: {format(new Date(expense.date), "dd/MM/yyyy")}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Date */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <label className="text-sm font-medium">{t('expenses.details.date')}</label>
              </div>
              <p className="text-sm">{format(new Date(expense.date), "dd/MM/yyyy")}</p>
            </div>

            {/* Amount - Make it more prominent */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <TurkishLiraIcon className="h-4 w-4 text-muted-foreground" />
                <label className="text-sm font-medium">{t('expenses.details.amount')}</label>
              </div>
              <p className="text-2xl font-bold text-primary">{parseFloat(expense.amount).toFixed(2)} {t('common.currency')}</p>
            </div>

            {/* Expense Type */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Tag className="h-4 w-4 text-muted-foreground" />
                <label className="text-sm font-medium">{t('expenses.details.expenseType')}</label>
              </div>
              <Badge variant="outline">
                {expense.instructor && expense.facility 
                  ? t('expenses.types.instructorFacility')
                  : expense.instructor 
                    ? t('expenses.types.instructorOnly')
                    : expense.facility
                      ? t('expenses.types.facilityOnly')
                      : t('expenses.types.general')
                }
              </Badge>
            </div>

            {/* Category */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Tag className="h-4 w-4 text-muted-foreground" />
                <label className="text-sm font-medium">{t('expenses.details.category')}</label>
              </div>
              <Badge variant="secondary" className="capitalize">
                {t(`expenses.categories.${expense.category}`)}
              </Badge>
            </div>

            {/* Instructor - Enhanced with avatar */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <label className="text-sm font-medium">{t('expenses.details.instructor')}</label>
              </div>
              {expense.instructor ? (
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-primary">
                      {expense.instructor.name.charAt(0)}{expense.instructor.surname.charAt(0)}
                    </span>
                  </div>
                  <p className="text-sm font-medium">
                    {expense.instructor.name} {expense.instructor.surname}
                  </p>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">{t('common.notAssigned')}</p>
              )}
            </div>

            {/* Facility - Enhanced with icon */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Building className="h-4 w-4 text-muted-foreground" />
                <label className="text-sm font-medium">{t('expenses.details.facility')}</label>
              </div>
              {expense.facility ? (
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-blue-600">
                      {expense.facility.name.charAt(0)}
                    </span>
                  </div>
                  <p className="text-sm font-medium">{expense.facility.name}</p>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">{t('common.notAssigned')}</p>
              )}
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <FileText className="h-4 w-4 text-muted-foreground" />
              <label className="text-sm font-medium">{t('expenses.details.description')}</label>
            </div>
            <div className="bg-muted/50 rounded-lg p-4">
              <p className="text-sm whitespace-pre-wrap">
                {expense.description || t('common.noDescription')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
