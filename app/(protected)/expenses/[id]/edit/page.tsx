import { getExpenseById } from "@/lib/actions/expenses";
import { getInstructors } from "@/lib/actions/instructors";
import { getFacilities } from "@/lib/actions/facilities";
import { notFound } from "next/navigation";
import EditExpenseClient from "./edit-expense-client";

interface EditExpensePageProps {
  params: Promise<{ id: string }>;
}

export default async function EditExpensePage({ params }: EditExpensePageProps) {
  const { id } = await params;
  
  const [expense, instructors, facilities] = await Promise.all([
    getExpenseById(id),
    getInstructors(),
    getFacilities(),
  ]);

  if (!expense) {
    notFound();
  }

  return <EditExpenseClient expense={expense} instructors={instructors} facilities={facilities} />;
}
