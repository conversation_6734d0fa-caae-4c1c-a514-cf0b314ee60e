"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { Textarea } from "@/components/ui/textarea";
import Link from "next/link";
import { ArrowLeft, Save, RotateCcw } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { createExpense } from "@/lib/actions/expenses";
import { Instructor, Facility } from "@/lib/types";
import {useToast} from "@/hooks/use-toast";

interface NewExpenseClientProps {
  instructors: Instructor[];
  facilities: Facility[];
}

export default function NewExpenseClient({ instructors, facilities }: NewExpenseClientProps) {
  const router = useRouter();
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [expense, setExpense] = useState({
    amount: "",
    category: "",
    date: undefined as Date | undefined,
    description: "",
    instructorId: "none",
    facilityId: "none",
  });

  const resetForm = () => {
    setExpense({
      amount: "",
      category: "",
      date: undefined,
      description: "",
      instructorId: "none",
      facilityId: "none",
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!expense.amount || !expense.category || !expense.date || !expense.description) {
      toast({
        title: t('common.error'),
        description: t('expenses.messages.requiredFields', 'Please fill in all required fields'),
        variant: "destructive",
      });
      return;
    }

    // Validate amount is positive
    const amount = parseFloat(expense.amount);
    if (isNaN(amount) || amount <= 0) {
      toast({
        title: t('common.error'),
        description: t('expenses.messages.invalidAmount', 'Please enter a valid amount'),
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      const result = await createExpense({
        amount: expense.amount,
        category: expense.category as 'salary' | 'insurance' | 'rent' | 'equipment' | 'other',
        date: expense.date.toISOString().split('T')[0], // Format as YYYY-MM-DD
        description: expense.description,
        instructorId: expense.instructorId === "none" ? undefined : expense.instructorId,
        facilityId: expense.facilityId === "none" ? undefined : expense.facilityId,
      });
      if(result.success){
        toast({
          title: t('common.success'),
          description: t('expenses.messages.createSuccess', 'Expense created successfully'),
        });
        router.push("/expenses");
      }else{
        let errorDescriptionKey = '';
        if(result.errorType == 'BusinessRuleError'){
          errorDescriptionKey = `errors.${result.error}`;
        }else{
          errorDescriptionKey = 'expenses.messages.createError';
        }
        toast({
          title: t('common.error'),
          description: t(errorDescriptionKey),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error creating expense:', error);
      toast({
        title: t('common.error'),
        description: t('expenses.messages.createError', 'Failed to create expense'),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Button
        variant="ghost"
        className="mb-6"
        asChild
      >
        <Link href="/expenses">
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('common.actions.back')}
        </Link>
      </Button>
      
      <Card>
        <CardHeader>
          <CardTitle>{t('expenses.new')}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="amount">{t('expenses.details.amount')} *</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    min="0"
                    value={expense.amount}
                    onChange={(e) => setExpense(prev => ({ ...prev, amount: e.target.value }))}
                    placeholder={t('expenses.placeholders.enterAmount')}
                    className="max-w-[200px]"
                    disabled={loading}
                  />
                  <span className="text-sm text-muted-foreground">{t('common.currency')}</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="category">{t('expenses.details.category')} *</Label>
                <Select
                  value={expense.category}
                  onValueChange={(value) => setExpense(prev => ({ ...prev, category: value }))}
                  disabled={loading}
                >
                  <SelectTrigger className="max-w-[200px]">
                    <SelectValue placeholder={t('expenses.placeholders.selectCategory')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="salary">{t('expenses.categories.salary')}</SelectItem>
                    <SelectItem value="insurance">{t('expenses.categories.insurance')}</SelectItem>
                    <SelectItem value="rent">{t('expenses.categories.rent')}</SelectItem>
                    <SelectItem value="equipment">{t('expenses.categories.equipment')}</SelectItem>
                    <SelectItem value="other">{t('expenses.categories.other')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>{t('expenses.details.date')} *</Label>
                <DatePicker
                  date={expense.date}
                  onSelect={(date) => setExpense(prev => ({ ...prev, date }))}
                  placeholder={t('expenses.placeholders.selectDate')}
                />
              </div>

              {instructors.length > 0 && (
                <div className="space-y-2">
                  <Label htmlFor="instructor">{t('expenses.details.instructor')}</Label>
                  <Select
                    value={expense.instructorId}
                    onValueChange={(value) => setExpense(prev => ({ ...prev, instructorId: value }))}
                    disabled={loading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t('expenses.placeholders.selectInstructor')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">{t('common.actions.none')}</SelectItem>
                      {instructors.map((instructor) => (
                        <SelectItem key={instructor.id} value={instructor.id}>
                          {instructor.name} {instructor.surname}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {facilities.length > 0 && (
                <div className="space-y-2">
                  <Label htmlFor="facility">{t('expenses.details.facility')}</Label>
                  <Select
                    value={expense.facilityId}
                    onValueChange={(value) => setExpense(prev => ({ ...prev, facilityId: value }))}
                    disabled={loading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t('expenses.placeholders.selectFacility')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">{t('common.actions.none')}</SelectItem>
                      {facilities.map((facility) => (
                        <SelectItem key={facility.id} value={facility.id}>
                          {facility.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">{t('expenses.details.description')} *</Label>
              <Textarea
                id="description"
                value={expense.description}
                onChange={(e) => setExpense(prev => ({ ...prev, description: e.target.value }))}
                placeholder={t('expenses.placeholders.enterDescription')}
                rows={3}
                disabled={loading}
              />
            </div>
            
            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={resetForm}
                disabled={loading}
              >
                <RotateCcw className="mr-2 h-4 w-4" />
                {t('common.actions.reset')}
              </Button>
              <Button
                type="button"
                variant="outline"
                asChild
              >
                <Link href="/expenses">{t('common.actions.cancel')}</Link>
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-foreground" />
                    {t('common.actions.saving')}
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    {t('expenses.actions.save')}
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
