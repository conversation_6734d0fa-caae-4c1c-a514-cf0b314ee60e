import { getInstructors } from "@/lib/actions/instructors";
import { getFacilities } from "@/lib/actions/facilities";
import NewExpenseClient from "./new-expense-client";

export default async function NewExpensePage() {
  const [instructors, facilities] = await Promise.all([
    getInstructors(),
    getFacilities(),
  ]);
  
  return <NewExpenseClient instructors={instructors} facilities={facilities} />;
}
