"use client";

import { useRouter } from "next/navigation";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, Pencil, Users, Calendar, MapPin, Clock, UserMinus, Download, MessageSquare } from "lucide-react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { TeamPaymentPlanBulkAssignment } from "@/components/teams/team-payment-plan-bulk-assignment";
import { AddAthleteToTeamDialog } from "@/components/teams/add-athlete-to-team-dialog";
import { RemoveAthleteFromTeamDialog } from "@/components/teams/remove-athlete-from-team-dialog";
import type { Team, Athlete } from "@/lib/types";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { generateTeamSchedulePDF } from "@/lib/pdf-utils";
import TeamMessageDialog from "@/components/sms/team-message-dialog";

interface PaymentPlan {
  id: string;
  name: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
  status: "active" | "inactive";
  branches?: { id: string; name: string; description: string | null; }[];
}

interface TeamDetailClientProps {
  team: Team | null;
  athletes: Athlete[];
  availablePaymentPlans: PaymentPlan[];
}

function getDayName(day: number): string {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  return days[day] || 'Unknown';
}

export default function TeamDetailClient({ team, athletes, availablePaymentPlans }: TeamDetailClientProps) {
  const router = useRouter();
  const { t } = useSafeTranslation();

  const handleAssignmentComplete = () => {
    // Refresh the page to show updated data
    router.refresh();
  };

  if (!team) {
    return (
      <div className="space-y-6">
        <Button variant="ghost" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('common.actions.back')}
        </Button>
        <Card>
          <CardHeader>
            <CardTitle>{t('common.noData')}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              {t('common.error')}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('common.actions.back')}
        </Button>
        <div className="flex items-center space-x-2">
          <TeamMessageDialog
            team={team}
            athletes={athletes}
            trigger={
              <Button variant="outline">
                <MessageSquare className="mr-2 h-4 w-4" />
                {t('sms:actions.sendTeamSms')}
              </Button>
            }
          />
          <Button asChild>
            <Link href={`/teams/${team.id}/edit`}>
              <Pencil className="mr-2 h-4 w-4" />
              {t('common.actions.edit')}
            </Link>
          </Button>
        </div>
      </div>

      {/* Team Header */}
      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-2xl">{team.name}</CardTitle>
                {team.description && (
                  <p className="text-muted-foreground mt-2">{team.description}</p>
                )}
              </div>
              <Badge variant="outline" className="px-3 py-1">
                {team.branch?.name ? t(`common.branches.${team.branch.name}`, { ns: 'shared' }) : '-'}
              </Badge>
            </div>
          </CardHeader>
        </Card>

        {/* Team Information Grid */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* School & Instructor Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                {t('teams.details.schoolAndInstructor')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-1">{t('schools.title')}</h4>
                <p className="text-muted-foreground">{team.school?.name}</p>
              </div>
              <div>
                <h4 className="font-medium mb-1">{t('instructors.title')}</h4>
                <p className="text-muted-foreground">
                  {team.instructor?.name} {team.instructor?.surname}
                </p>
              </div>
              <div>
                <h4 className="font-medium mb-1">{t('teams.details.branch')}</h4>
                <Badge variant="secondary">
                  {team.branch?.name ? t(`common.branches.${team.branch.name}`, { ns: 'shared' }) : '-'}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Athletes Summary */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  {t('athletes.title')} ({athletes.length})
                </CardTitle>
                <AddAthleteToTeamDialog
                  teamId={team.id}
                  teamName={team.name}
                  teamBranchId={team.branchId}
                  existingAthleteIds={athletes.map(a => a.id)}
                  onAthleteAdded={handleAssignmentComplete}
                />
              </div>
            </CardHeader>
            <CardContent>
              {athletes.length > 0 ? (
                <div className="space-y-3">
                  <div className="grid grid-cols-1 gap-2 max-h-64 overflow-y-auto">
                    {athletes.map((athlete) => (
                      <div key={athlete.id} className="flex items-center justify-between p-3 bg-muted/40 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div>
                            <span className="font-medium">{athlete.name} {athlete.surname}</span>
                            <Badge variant={athlete.status === 'active' ? 'default' : 'secondary'} className="text-xs ml-2">
                              {t(`common.status.${athlete.status}`)}
                            </Badge>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/athletes/${athlete.id}`}>
                              {t('common.actions.view')}
                            </Link>
                          </Button>
                          <RemoveAthleteFromTeamDialog
                            athleteId={athlete.id}
                            athleteName={athlete.name}
                            athleteSurname={athlete.surname}
                            teamId={team.id}
                            teamName={team.name}
                            onAthleteRemoved={handleAssignmentComplete}
                            triggerComponent={
                              <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                                <UserMinus className="h-4 w-4" />
                              </Button>
                            }
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="pt-2 border-t">
                    <Button variant="outline" size="sm" asChild className="w-full">
                      <Link href={`/athletes?team=${team.id}`}>
                        {t('teams.details.viewAllAthletes')}
                      </Link>
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-6">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                  <p className="text-muted-foreground mb-3">{t('teams.details.noAthletes')}</p>
                  <AddAthleteToTeamDialog
                    teamId={team.id}
                    teamName={team.name}
                    teamBranchId={team.branchId}
                    existingAthleteIds={[]}
                    onAthleteAdded={handleAssignmentComplete}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Team Payment Plan Bulk Assignment */}
        {athletes.length > 0 && (
          <TeamPaymentPlanBulkAssignment
            teamId={team.id}
            teamName={team.name}
            athletes={athletes}
            availablePaymentPlans={availablePaymentPlans}
            teamBranchId={team.branchId}
            onAssignmentComplete={handleAssignmentComplete}
          />
        )}

        {/* Training Schedule */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                {t('teams.details.schedule')}
              </CardTitle>
              {team.trainingSchedule && team.trainingSchedule.length > 0 && (
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    const pdf = generateTeamSchedulePDF({ team, t });
                    pdf.save(`${team.name}-schedule.pdf`);
                  }}
                >
                  <Download className="mr-2 h-4 w-4" />
                  {t('teams.details.downloadSchedule', { defaultValue: 'Download Schedule' })}
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {team.trainingSchedule && team.trainingSchedule.length > 0 ? (
              <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
                {team.trainingSchedule.map((schedule) => (
                  <div
                    key={schedule.id}
                    className="flex flex-col p-4 bg-muted/40 rounded-lg border"
                  >
                    <div className="flex items-center gap-2 mb-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">
                        {t(`teams.days.${getDayName(schedule.dayOfWeek).toLowerCase()}`)}
                      </span>
                    </div>
                    <div className="text-lg font-semibold text-primary">
                      {schedule.startTime} - {schedule.endTime}
                    </div>
                    {schedule.facility?.name && (
                      <div className="text-sm text-muted-foreground mt-1">
                        {t('facilities.title')}: {schedule.facility.name}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                <p className="text-muted-foreground">{t('teams.details.noSchedule')}</p>
                <Button variant="outline" size="sm" asChild className="mt-2">
                  <Link href={`/teams/${team.id}/edit`}>
                    {t('teams.details.setupSchedule')}
                  </Link>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
