import { Suspense } from 'react';
import { getSchools, getInstructors, getFacilities } from '@/lib/actions';
import NewTeamClient from './new-team-client';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

function LoadingSkeleton() {
  return (
    <div className="space-y-6">
      <Button variant="ghost" size="icon" asChild>
        <Link href="/teams">
          <ArrowLeft className="h-4 w-4" />
        </Link>
      </Button>
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            {[1, 2, 3, 4, 5, 6].map(i => (
              <div key={i} className="h-10 bg-muted animate-pulse rounded-md" />
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default async function NewTeamPage() {
  try {
    const [schools, instructors, facilities] = await Promise.all([
      getSchools(),
      getInstructors(),
      getFacilities()
    ]);

    return (
      <Suspense fallback={<LoadingSkeleton />}>
        <NewTeamClient 
          schools={schools}
          instructors={instructors}
          facilities={facilities}
        />
      </Suspense>
    );
  } catch (error) {
    console.error('Error loading new team data:', error);
    return (
      <div className="space-y-6">
        <Button variant="ghost" size="icon" asChild>
          <Link href="/teams">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-muted-foreground">Failed to load page data. Please try again.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
}
