'use client';

import { useState, useTransition, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Plus, X, Eye } from 'lucide-react';
import { createSmsConfiguration } from '@/lib/actions/sms';
import { useSafeTranslation } from '@/hooks/use-safe-translation';
import {useToast} from "@/hooks/use-toast";

const smsConfigurationSchema = z.object({
  pendingPaymentTemplate: z.string().min(10, 'Template must be at least 10 characters'),
  overduePaymentTemplate: z.string().min(10, 'Template must be at least 10 characters'),
  pendingReminderDays: z.array(z.number()).min(1, 'At least one reminder day is required'),
  overdueReminderDays: z.array(z.number()).min(1, 'At least one reminder day is required'),
});

type SmsConfigurationFormData = z.infer<typeof smsConfigurationSchema>;

interface SmsConfigurationFormProps {
  initialData?: any;
  onSuccess?: () => void;
}

export default function SmsConfigurationForm({ initialData, onSuccess }: SmsConfigurationFormProps) {
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [isPending, startTransition] = useTransition();
  const [pendingDayInput, setPendingDayInput] = useState('');
  const [overdueDayInput, setOverdueDayInput] = useState('');
  const [previewTemplate, setPreviewTemplate] = useState<'pending' | 'overdue' | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm<SmsConfigurationFormData>({
    resolver: zodResolver(smsConfigurationSchema),
    defaultValues: {
      pendingPaymentTemplate: initialData?.pendingPaymentTemplate || t('sms:configuration.templates.pending.default'),
      overduePaymentTemplate: initialData?.overduePaymentTemplate || t('sms:configuration.templates.overdue.default'),
      pendingReminderDays: initialData?.pendingReminderDays || [-5, -3, -1],
      overdueReminderDays: initialData?.overdueReminderDays || [1, 3, 5],
    }
  });

  // Update form values when language changes or when initialData is not available
  useEffect(() => {
    if (!initialData?.pendingPaymentTemplate) {
      setValue('pendingPaymentTemplate', t('sms:configuration.templates.pending.default'));
    }
    if (!initialData?.overduePaymentTemplate) {
      setValue('overduePaymentTemplate', t('sms:configuration.templates.overdue.default'));
    }
  }, [t, initialData, setValue]);

  const pendingReminderDays = watch('pendingReminderDays');
  const overdueReminderDays = watch('overdueReminderDays');
  const pendingTemplate = watch('pendingPaymentTemplate');
  const overdueTemplate = watch('overduePaymentTemplate');

  // Calculate SMS count based on message length
  const calculateSmsCount = (length: number) => {
    if (length === 0) return 0;
    if (length <= 160) return 1;
    if (length <= 306) return 2;
    if (length <= 459) return 3;
    if (length <= 612) return 4;
    return Math.ceil(length / 153); // For messages longer than 612 chars
  };

  const pendingTemplateLength = pendingTemplate?.length || 0;
  const overdueTemplateLength = overdueTemplate?.length || 0;
  const pendingSmsCount = calculateSmsCount(pendingTemplateLength);
  const overdueSmsCount = calculateSmsCount(overdueTemplateLength);

  const addPendingDay = () => {
    const day = parseInt(pendingDayInput);
    if (!isNaN(day) && day < 0 && !pendingReminderDays.includes(day)) {
      setValue('pendingReminderDays', [...pendingReminderDays, day].sort((a, b) => b - a));
      setPendingDayInput('');
    }
  };

  const addOverdueDay = () => {
    const day = parseInt(overdueDayInput);
    if (!isNaN(day) && day > 0 && !overdueReminderDays.includes(day)) {
      setValue('overdueReminderDays', [...overdueReminderDays, day].sort((a, b) => a - b));
      setOverdueDayInput('');
    }
  };

  const removePendingDay = (day: number) => {
    setValue('pendingReminderDays', pendingReminderDays.filter(d => d !== day));
  };

  const removeOverdueDay = (day: number) => {
    setValue('overdueReminderDays', overdueReminderDays.filter(d => d !== day));
  };

  const processTemplatePreview = (template: string) => {
    return template
      .replace(/\{\{athleteName\}\}/g, t('sms:configuration.preview.athleteName'))
      .replace(/\{\{parentName\}\}/g, t('sms:configuration.preview.parentName'))
      .replace(/\{\{amount\}\}/g, t('sms:configuration.preview.amount'))
      .replace(/\{\{teamName\}\}/g, t('sms:configuration.preview.teamName'))
      .replace(/\{\{schoolName\}\}/g, t('sms:configuration.preview.schoolName'))
      .replace(/\{\{paymentDueDate\}\}/g, t('sms:configuration.preview.paymentDueDate'));
  };

  const onSubmit = (data: SmsConfigurationFormData) => {
    startTransition(async () => {
      try {
        const result = await createSmsConfiguration(data);
        if (result.success) {
          toast({
            title: t('common.success'),
            description: t('sms:configuration.messages.success'),
          });
          reset(data);
          onSuccess?.(); // Refresh parent component
        }
      } catch (error) {
        toast({
          title: t('common.error'),
          description: t('sms:configuration.messages.error'),
          variant: "destructive",
        });
      }
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Pending Payment Template */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label htmlFor="pendingPaymentTemplate" className="text-base font-medium">
            {t('sms:configuration.templates.pending.title')}
          </Label>
          <div className="flex space-x-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setValue('pendingPaymentTemplate', t('sms:configuration.templates.pending.default'))}
            >
              {t('sms:configuration.actions.reset')}
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setPreviewTemplate(previewTemplate === 'pending' ? null : 'pending')}
            >
              <Eye className="h-4 w-4 mr-2" />
              {t('sms:configuration.actions.preview')}
            </Button>
          </div>
        </div>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 text-xs">
              <span className="text-muted-foreground">
                {pendingTemplateLength} {t('sms:common.characters')}
              </span>
              <span className={`font-medium ${pendingSmsCount > 1 ? 'text-orange-600' : 'text-green-600'}`}>
                ~{pendingSmsCount} SMS {t('sms:common.approximately')}
              </span>
            </div>
          </div>
          <Textarea
            id="pendingPaymentTemplate"
            {...register('pendingPaymentTemplate')}
            placeholder={t('sms:configuration.templates.pending.placeholder')}
            rows={3}
            className={errors.pendingPaymentTemplate ? 'border-red-500' : ''}
          />
        </div>
        {errors.pendingPaymentTemplate && (
          <p className="text-sm text-red-500">{errors.pendingPaymentTemplate.message}</p>
        )}
        {previewTemplate === 'pending' && (
          <Alert>
            <AlertDescription>
              <strong>Preview:</strong> {processTemplatePreview(pendingTemplate)}
            </AlertDescription>
          </Alert>
        )}
      </div>

      {/* Pending Reminder Days */}
      <div className="space-y-4">
        <Label className="text-base font-medium">{t('sms:configuration.templates.pending.days')}</Label>
        <p className="text-sm text-muted-foreground">
          {t('sms:configuration.templates.pending.daysDescription')}
        </p>
        <div className="flex space-x-2">
          <Input
            type="number"
            placeholder="-5"
            value={pendingDayInput}
            onChange={(e) => setPendingDayInput(e.target.value)}
            className="w-24"
          />
          <Button type="button" onClick={addPendingDay} size="sm">
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex flex-wrap gap-2">
          {pendingReminderDays.map((day) => (
            <Badge key={day} variant="secondary" className="flex items-center space-x-1">
              <span>{day} {t('sms:common.days')}</span>
              <button
                type="button"
                onClick={() => removePendingDay(day)}
                className="ml-1 hover:text-red-500"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
        </div>
        {errors.pendingReminderDays && (
          <p className="text-sm text-red-500">{errors.pendingReminderDays.message}</p>
        )}
      </div>

      {/* Overdue Payment Template */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label htmlFor="overduePaymentTemplate" className="text-base font-medium">
            {t('sms:configuration.templates.overdue.title')}
          </Label>
          <div className="flex space-x-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setValue('overduePaymentTemplate', t('sms:configuration.templates.overdue.default'))}
            >
              {t('sms:configuration.actions.reset')}
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setPreviewTemplate(previewTemplate === 'overdue' ? null : 'overdue')}
            >
              <Eye className="h-4 w-4 mr-2" />
              {t('sms:configuration.actions.preview')}
            </Button>
          </div>
        </div>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 text-xs">
              <span className="text-muted-foreground">
                {overdueTemplateLength} {t('sms:common.characters')}
              </span>
              <span className={`font-medium ${overdueSmsCount > 1 ? 'text-orange-600' : 'text-green-600'}`}>
                ~{overdueSmsCount} SMS {t('sms:common.approximately')}
              </span>
            </div>
          </div>
          <Textarea
            id="overduePaymentTemplate"
            {...register('overduePaymentTemplate')}
            placeholder={t('sms:configuration.templates.overdue.placeholder')}
            rows={3}
            className={errors.overduePaymentTemplate ? 'border-red-500' : ''}
          />
        </div>
        {errors.overduePaymentTemplate && (
          <p className="text-sm text-red-500">{errors.overduePaymentTemplate.message}</p>
        )}
        {previewTemplate === 'overdue' && (
          <Alert>
            <AlertDescription>
              <strong>Preview:</strong> {processTemplatePreview(overdueTemplate)}
            </AlertDescription>
          </Alert>
        )}
      </div>

      {/* Overdue Reminder Days */}
      <div className="space-y-4">
        <Label className="text-base font-medium">{t('sms:configuration.templates.overdue.days')}</Label>
        <p className="text-sm text-muted-foreground">
          {t('sms:configuration.templates.overdue.daysDescription')}
        </p>
        <div className="flex space-x-2">
          <Input
            type="number"
            placeholder="1"
            value={overdueDayInput}
            onChange={(e) => setOverdueDayInput(e.target.value)}
            className="w-24"
          />
          <Button type="button" onClick={addOverdueDay} size="sm">
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex flex-wrap gap-2">
          {overdueReminderDays.map((day) => (
            <Badge key={day} variant="secondary" className="flex items-center space-x-1">
              <span>+{day} {t('sms:common.days')}</span>
              <button
                type="button"
                onClick={() => removeOverdueDay(day)}
                className="ml-1 hover:text-red-500"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
        </div>
        {errors.overdueReminderDays && (
          <p className="text-sm text-red-500">{errors.overdueReminderDays.message}</p>
        )}
      </div>

      {/* Submit Button */}
      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => reset()}
          disabled={isPending}
        >
          {t('sms:configuration.actions.reset')}
        </Button>
        <Button type="submit" disabled={isPending}>
          {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {t('sms:configuration.actions.save')}
        </Button>
      </div>
    </form>
  );
}
