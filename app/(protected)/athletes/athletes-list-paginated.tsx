"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { PlusCircle, Filter } from "lucide-react";
import Link from "next/link";
import { Athlete } from "@/lib/types";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { useColumns } from "@/components/athletes-table-columns";
import { AthleteImportDialog } from "@/components/athletes/import/AthleteImportDialog";
import { GenericListPage } from "@/components/ui/generic-list-page";
import { PaginationData, SearchParams } from "@/hooks/use-paginated-list";

interface AthletesListPaginatedProps {
  initialData: Athlete[];
  initialPagination: PaginationData;
  initialSearchParams: SearchParams & {
    name?: string;
    surname?: string;
    parentEmail?: string;
    parentPhone?: string;
    nationalId?: string;
    status?: 'active' | 'inactive' | 'suspended';
  };
}

export function AthletesListPaginated({
  initialData,
  initialPagination,
  initialSearchParams
}: AthletesListPaginatedProps) {
  const { t } = useSafeTranslation();
  const router = useRouter();
  const columns = useColumns();

  // Local state for filters
  const [filters, setFilters] = useState({
    name: initialSearchParams.name || "",
    surname: initialSearchParams.surname || "",
    parentEmail: initialSearchParams.parentEmail || "",
    parentPhone: initialSearchParams.parentPhone || "",
    nationalId: initialSearchParams.nationalId || "",
    status: initialSearchParams.status || "",
  });
  const [showFilters, setShowFilters] = useState(
    !!(initialSearchParams.name || initialSearchParams.surname ||
       initialSearchParams.parentEmail || initialSearchParams.parentPhone ||
       initialSearchParams.nationalId || initialSearchParams.status)
  );

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleApplyFilters = () => {
    const searchParams = new URLSearchParams(window.location.search);

    // Update search params with filter values
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value.trim() !== '') {
        searchParams.set(key, value);
      } else {
        searchParams.delete(key);
      }
    });

    // Reset to page 1 when applying filters
    searchParams.set('page', '1');

    // Use router.push for smooth navigation without page refresh
    router.push(`/athletes?${searchParams.toString()}`);
  };

  const handleClearFilters = () => {
    // Reset all filter states
    setFilters({
      name: "",
      surname: "",
      parentEmail: "",
      parentPhone: "",
      nationalId: "",
      status: "",
    });

    // Clear URL parameters
    const searchParams = new URLSearchParams(window.location.search);

    // Remove filter parameters but keep search and pagination
    ['name', 'surname', 'parentEmail', 'parentPhone', 'nationalId', 'status'].forEach(key => {
      searchParams.delete(key);
    });

    // Reset to page 1
    searchParams.set('page', '1');

    // Use router.push for smooth navigation without page refresh
    router.push(`/athletes?${searchParams.toString()}`);
  };

  // Create actions for the header
  const actions = (
    <div className="flex items-center space-x-2">
      <AthleteImportDialog />
      <Button asChild>
        <Link href="/athletes/new">
          <PlusCircle className="mr-2 h-4 w-4" />
          {t('athletes.addAthlete')}
        </Link>
      </Button>
    </div>
  );

  // Create filters component
  const filtersComponent = showFilters ? (
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">{t('common.actions.filter')}</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(false)}
            >
              {t('common.actions.cancel')}
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t('athletes.details.name')}</Label>
              <Input
                id="name"
                value={filters.name}
                onChange={(e) => handleFilterChange('name', e.target.value)}
                placeholder={t('athletes.placeholders.firstName')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="surname">{t('athletes.details.surname')}</Label>
              <Input
                id="surname"
                value={filters.surname}
                onChange={(e) => handleFilterChange('surname', e.target.value)}
                placeholder={t('athletes.placeholders.lastName')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">{t('athletes.details.status')}</Label>
              <Select value={filters.status || "all"} onValueChange={(value) => handleFilterChange('status', value === 'all' ? '' : value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('common.all')}</SelectItem>
                  <SelectItem value="active">{t('athletes.status.active')}</SelectItem>
                  <SelectItem value="inactive">{t('athletes.status.inactive')}</SelectItem>
                  <SelectItem value="suspended">{t('athletes.status.suspended')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button onClick={handleApplyFilters}>
              {t('common.actions.apply')}
            </Button>
            <Button variant="outline" onClick={handleClearFilters}>
              {t('common.actions.clear')}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  ) : (
    <Button
      variant="outline"
      onClick={() => setShowFilters(true)}
    >
      <Filter className="mr-2 h-4 w-4" />
      {t('common.actions.filter')}
    </Button>
  );

  return (
    <GenericListPage
      data={initialData}
      pagination={initialPagination}
      columns={columns}
      title={t('athletes.title')}
      description={t('athletes.messages.manageAthletes')}
      basePath="/athletes"
      initialSearchParams={initialSearchParams}
      actions={actions}
      filters={filtersComponent}
      searchPlaceholder={t('athletes.placeholders.searchAthletes')}
      paginationOptions={{
        defaultSortBy: 'createdAt',
        defaultSortOrder: 'desc',
        searchMinLength: 3,
        searchDebounceMs: 500,
      }}
    />
  );
}
