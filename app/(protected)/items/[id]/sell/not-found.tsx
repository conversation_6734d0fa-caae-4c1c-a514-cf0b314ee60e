import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Package, ArrowLeft, Search } from "lucide-react";
import Link from "next/link";

export default function SellItemNotFound() {
  return (
    <div className="container mx-auto py-6">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 h-12 w-12 text-muted-foreground">
              <Package className="h-full w-full" />
            </div>
            <CardTitle className="text-xl">Item Not Available</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 text-center">
            <p className="text-muted-foreground">
              The item you&apos;re trying to sell is not available. This could be because:
            </p>
            
            <ul className="text-left space-y-2 text-sm text-muted-foreground max-w-md mx-auto">
              <li className="flex items-center gap-2">
                <span className="h-1.5 w-1.5 bg-muted-foreground rounded-full flex-shrink-0" />
                The item doesn&apos;t exist
              </li>
              <li className="flex items-center gap-2">
                <span className="h-1.5 w-1.5 bg-muted-foreground rounded-full flex-shrink-0" />
                The item is out of stock
              </li>
              <li className="flex items-center gap-2">
                <span className="h-1.5 w-1.5 bg-muted-foreground rounded-full flex-shrink-0" />
                You don&apos;t have permission to access this item
              </li>
            </ul>

            <div className="flex flex-col sm:flex-row gap-3 justify-center pt-4">
              <Button 
                asChild
                variant="default"
                className="inline-flex items-center gap-2"
              >
                <Link href="/items">
                  <Search className="h-4 w-4" />
                  Browse Items
                </Link>
              </Button>
              
              <Button 
                asChild
                variant="outline"
                className="inline-flex items-center gap-2"
              >
                <Link href="/dashboard">
                  <ArrowLeft className="h-4 w-4" />
                  Back to Dashboard
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
