"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { DatePicker } from "@/components/ui/date-picker";
import { ArrowLeft, Package2 } from "lucide-react";
import Link from "next/link";
import { SecureImage } from "@/components/ui/secure-image";
import { Athlete, Item } from "@/lib/types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { createItemSale } from "@/lib/actions";
import {useToast} from "@/hooks/use-toast";

interface SellItemClientProps {
  item: Item;
  athletes: Athlete[];
}

export default function SellItemClient({ item, athletes }: SellItemClientProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const { t } = useSafeTranslation();
  const [sale, setSale] = useState({
    athleteId: "",
    quantity: 1,
    paymentStatus: "completed" as "pending" | "completed" | "overdue",
    paymentMethod: "none" as string,
    billingDate: new Date() as Date | undefined,
    dueDate: (() => {
      const defaultDueDate = new Date();
      defaultDueDate.setDate(defaultDueDate.getDate() + 10);
      return defaultDueDate;
    })() as Date | undefined,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!item || !sale.athleteId || sale.quantity < 1) {
      toast({
        title: t('common.error'),
        description: t('items.sell.errors.invalidData'),
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      
      const result = await createItemSale({
        itemId: item.id,
        athleteId: sale.athleteId,
        quantity: sale.quantity,
        paymentStatus: sale.paymentStatus,
        paymentMethod: sale.paymentMethod === "" || sale.paymentMethod === "none" ? null : sale.paymentMethod as "cash" | "bank_transfer" | "credit_card",
        billingDate: sale.billingDate ? sale.billingDate.toISOString().split('T')[0] : undefined,
        dueDate: sale.dueDate ? sale.dueDate.toISOString().split('T')[0] : undefined,
      });
      if(result.success){
        toast({
          title: t('common.success'),
          description: t('items.sell.messages.saleCompleted'),
        });
        router.push("/items");
      }else{
        let errorDescriptionKey = '';
        if(result.errorType == 'BusinessRuleError'){
          errorDescriptionKey = `errors.${result.error}`;
        }else{
          errorDescriptionKey = 'items.sell.errors.saleError';
        }
        toast({
          title: t('common.error'),
          description: t(errorDescriptionKey),
          variant: "destructive",
        });
      }

    } catch (error) {
      console.error("Error processing sale:", error);
      toast({
        title: t('common.error'),
        description: t('items.sell.errors.saleError'),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const totalPrice = parseFloat(item.price) * sale.quantity;

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Link href="/items">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold tracking-tight">{t('items.sell.title')}</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>{t('items.sell.itemDetails')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="relative aspect-square">
                <SecureImage
                  src={item.image || ""}
                  alt={item.name}
                  fill
                  className="object-cover rounded-md"
                  placeholderIcon={Package2}
                />
              </div>

              <div className="space-y-4">
                <h3 className="font-medium">{item.name}</h3>
                {item.description && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {item.description}
                  </p>
                )}
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('items.details.price')}:</span>
                <span className="font-medium">{item.price} {t('common.currency')}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('items.sell.availableStock')}:</span>
                <span className="font-medium">{item.stock}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('items.sell.saleInformation')}</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>{t('items.sell.selectAthlete')} *</Label>
                  <Select
                    value={sale.athleteId}
                    onValueChange={(value) =>
                      setSale((prev) => ({ ...prev, athleteId: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t('items.placeholders.chooseAthlete')} />
                    </SelectTrigger>
                    <SelectContent>
                      {athletes.map((athlete) => (
                        <SelectItem key={athlete.id} value={athlete.id}>
                          {athlete.name} {athlete.surname}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>{t('items.sell.quantity')} *</Label>
                  <Input
                    type="number"
                    min="1"
                    max={item.stock}
                    value={sale.quantity}
                    onChange={(e) =>
                      setSale((prev) => ({ ...prev, quantity: parseInt(e.target.value) || NaN}))
                    }
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label>{t('items.sell.paymentStatus')} *</Label>
                  <Select
                    value={sale.paymentStatus}
                    onValueChange={(value) =>
                      setSale((prev) => ({
                        ...prev,
                        paymentStatus: value as "pending" | "completed" | "overdue",
                        // Reset payment method if status is not completed
                        paymentMethod: value === "completed" ? prev.paymentMethod : "none"
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t('items.sell.selectPaymentStatus')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="completed">{t('items.paymentStatus.completed')}</SelectItem>
                      <SelectItem value="pending">{t('items.paymentStatus.pending')}</SelectItem>
                      <SelectItem value="overdue">{t('items.paymentStatus.overdue')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Payment Method - only show when payment status is completed */}
                {sale.paymentStatus === "completed" && (
                  <div className="space-y-2">
                    <Label>{t('payments.details.method')}</Label>
                    <Select
                      value={sale.paymentMethod || "none"}
                      onValueChange={(value) =>
                        setSale((prev) => ({ ...prev, paymentMethod: value === "none" ? "" : value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={t('payments.selectMethod')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">{t('payments.methods.not_specified')}</SelectItem>
                        <SelectItem value="cash">{t('payments.methods.cash')}</SelectItem>
                        <SelectItem value="bank_transfer">{t('payments.methods.bank_transfer')}</SelectItem>
                        <SelectItem value="credit_card">{t('payments.methods.credit_card')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div className="space-y-2">
                  <Label>{t('items.sell.billingDate')} *</Label>
                  <DatePicker
                    date={sale.billingDate}
                    onSelect={(date) =>
                      setSale((prev) => ({ ...prev, billingDate: date }))
                    }
                    placeholder={t('items.sell.selectBillingDate')}
                  />
                </div>

                <div className="space-y-2">
                  <Label>{t('items.sell.dueDate')} *</Label>
                  <DatePicker
                    date={sale.dueDate}
                    onSelect={(date) =>
                      setSale((prev) => ({ ...prev, dueDate: date }))
                    }
                    placeholder={t('items.sell.selectDueDate')}
                  />
                </div>

                <div className="pt-4 border-t">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-muted-foreground">{t('items.sell.unitPrice')}:</span>
                    <span>{item.price} {t('common.currency')}</span>
                  </div>
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-sm text-muted-foreground">{t('items.sell.quantity')}:</span>
                    <span>× {sale.quantity}</span>
                  </div>
                  <div className="flex items-center justify-between text-lg font-medium">
                    <span>{t('items.sell.total')}:</span>
                    <span>{totalPrice.toFixed(2)} {t('common.currency')}</span>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push("/items")}
                  disabled={loading}
                >
                  {t('common.actions.cancel')}
                </Button>
                <Button 
                  type="submit" 
                  disabled={loading || !sale.athleteId || sale.quantity < 1 || sale.quantity > item.stock}
                >
                  {loading ? t('items.sell.processing') : t('items.sell.completeSale')}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
