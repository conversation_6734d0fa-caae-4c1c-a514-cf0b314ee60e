"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowLeft, Upload, Package2 } from "lucide-react";
import Link from "next/link";
import { SecureImage } from "@/components/ui/secure-image";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { Item } from "@/lib/types";
import { updateItem } from "@/lib/actions";
import { uploadImage } from "@/lib/file-uploads";
import { useEffect } from "react";
import {useToast} from "@/hooks/use-toast";

interface EditItemClientProps {
  item: Item;
}

export default function EditItemClient({ item: initialItem }: EditItemClientProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(initialItem.image || null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [imageError, setImageError] = useState(false);
  const { t } = useSafeTranslation();

  // Cleanup object URL when component unmounts or file changes
  useEffect(() => {
    return () => {
      if (previewUrl && previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);
  const [item, setItem] = useState({
    name: initialItem.name,
    description: initialItem.description || "",
    price: initialItem.price,
    category: initialItem.category,
    stock: initialItem.stock.toString(),
    image: initialItem.image || "",
  });

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: t('common.error'),
          description: t('common.upload.maxSize', { size: '5MB' }),
          variant: 'destructive',
        });
        return;
      }

      // Clean up previous object URL if it exists
      if (previewUrl && previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(previewUrl);
      }

      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
      setSelectedFile(file);
      setImageError(false); // Reset error state when new file is selected
    }
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);

    try {
      const formData = new FormData(e.currentTarget);
      
      let imagePath: string | undefined = item.image;

      // Upload image only when form is submitted to avoid storing unnecessary files
      if (selectedFile) {
        const imageFormData = new FormData();
        imageFormData.append('file', selectedFile);
        imagePath = await uploadImage(imageFormData);
      }

      const itemData = {
        name: formData.get("name") as string,
        description: formData.get("description") as string,
        price: formData.get("price") as string,
        category: formData.get("category") as "equipment" | "clothing" | "accessories" | "other",
        stock: parseInt(formData.get("stock") as string),
        image: imagePath,
      };

      // Validate required fields
      if (!itemData.name || !itemData.price || !itemData.category) {
        toast({
          title: t('common.error'),
          description: t('items.errors.requiredFields'),
          variant: 'destructive',
        });
        return;
      }

      // Update item in database
      const result = await updateItem(initialItem.id, itemData);
      if(result.success){
        toast({
          title: t('common.success'),
          description: t('items.messages.updateSuccess'),
        });
        router.push("/items");
        router.refresh();
      }else{
        let errorDescriptionKey = '';
        if(result.errorType == 'BusinessRuleError'){
          errorDescriptionKey = `errors.${result.error}`;
        }else{
          errorDescriptionKey = 'items.messages.updateError';
        }
        toast({
          title: t('common.error'),
          description: t(errorDescriptionKey),
          variant: 'destructive',
        });
      }

    } catch (error) {
      console.error("Failed to update item:", error);
      toast({
        title: t('common.error'),
        description: t('items.messages.updateError'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Link href="/items">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold tracking-tight">{t('items.edit')}</h1>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('items.details.title')}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>{t('items.details.image')}</Label>
                <div className="flex flex-col items-center justify-center w-full">
                  <label
                    htmlFor="image"
                    className="flex flex-col items-center justify-center w-full h-64 border-2 border-dashed rounded-lg cursor-pointer bg-muted/40 hover:bg-muted/60 transition-colors relative overflow-hidden"
                  >
                    {previewUrl && !imageError ? (
                      <div className="relative w-full h-full">
                        <SecureImage
                          src={previewUrl}
                          alt="Preview"
                          fill
                          className="object-contain p-4"
                          onError={handleImageError}
                          placeholderIcon={Package2}
                        />
                        <div className="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 hover:opacity-100 transition-opacity">
                          <p className="text-white flex items-center">
                            <Upload className="w-6 h-6 mr-2" />
                            {t('items.actions.changeImage')}
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <Upload className="w-8 h-8 mb-4 text-muted-foreground" />
                        <p className="mb-2 text-sm text-muted-foreground">
                          {t('items.placeholders.uploadImage')}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {t('items.placeholders.imageFormats')}
                        </p>
                      </div>
                    )}
                    <Input
                      id="image"
                      name="image"
                      type="file"
                      accept="image/png,image/jpeg,image/webp"
                      className="hidden"
                      onChange={handleImageChange}
                    />
                  </label>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="name">{t('items.details.name')} *</Label>
                <Input
                  id="name"
                  name="name"
                  value={item.name}
                  onChange={(e) => setItem(prev => ({ ...prev, name: e.target.value }))}
                  placeholder={t('items.placeholders.name')}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">{t('items.details.description')}</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={item.description}
                  onChange={(e) => setItem(prev => ({ ...prev, description: e.target.value }))}
                  placeholder={t('items.placeholders.description')}
                  className="resize-none"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="price">{t('items.details.price')} *</Label>
                  <Input
                    id="price"
                    name="price"
                    type="number"
                    min="0"
                    step="0.01"
                    value={item.price}
                    onChange={(e) => setItem(prev => ({ ...prev, price: e.target.value }))}
                    placeholder="0.00"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">{t('items.details.category')} *</Label>
                  <Select
                    name="category"
                    value={item.category}
                    onValueChange={(value: "equipment" | "clothing" | "accessories" | "other") => 
                      setItem(prev => ({ ...prev, category: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t('items.placeholders.category')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="equipment">{t('items.categories.equipment')}</SelectItem>
                      <SelectItem value="clothing">{t('items.categories.clothing')}</SelectItem>
                      <SelectItem value="accessories">{t('items.categories.accessories')}</SelectItem>
                      <SelectItem value="other">{t('items.categories.other')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="stock">{t('items.details.stock')} *</Label>
                  <Input
                    id="stock"
                    name="stock"
                    type="number"
                    min="0"
                    value={item.stock}
                    onChange={(e) => setItem(prev => ({ ...prev, stock: e.target.value }))}
                    placeholder="0"
                    required
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <Button
                variant="outline"
                onClick={() => router.push("/items")}
                type="button"
                disabled={loading}
              >
                {t('common.actions.cancel')}
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? t('common.actions.saving') : t('common.actions.save')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
