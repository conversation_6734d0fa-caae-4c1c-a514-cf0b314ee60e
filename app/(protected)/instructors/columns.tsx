"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { MoreHorizontal, Pencil, Trash, Eye } from "lucide-react";
import { Instructor } from "@/lib/types";
import Link from "next/link";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { useMemo, useState } from "react";
import { deleteInstructor } from "@/lib/actions";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";

const ActionsCell = ({ instructor }: { instructor: Instructor }) => {
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      const result = await deleteInstructor(instructor.id);
      
      if (result.success) {
        toast({
          title: t('instructors.messages.deleteSuccess'),
          description: t('instructors.messages.instructorDeleted'),
        });
        // Refresh the page to update the instructor list
        router.refresh();
      } else {
        let errorDescriptionKey = '';
        if(result.errorType == 'BusinessRuleError'){
          errorDescriptionKey = `errors.${result.error}`;
        }else{
          errorDescriptionKey = 'instructors.messages.deleteError';
        }
        toast({
          title: t('common.error'),
          description: t(errorDescriptionKey),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error deleting instructor:', error);
      toast({
        title: t('common.error'),
        description: t('instructors.messages.deleteError'),
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">{t('instructors.actions.openMenu')}</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>{t('common.actionsHeader')}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href={`/instructors/${instructor.id}`} className="flex items-center">
            <Eye className="mr-2 h-4 w-4" />
            {t('common.actions.view')}
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/instructors/${instructor.id}/edit`} className="flex items-center">
            <Pencil className="mr-2 h-4 w-4" />
            {t('instructors.actions.edit')}
          </Link>
        </DropdownMenuItem>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <DropdownMenuItem onSelect={(e) => e.preventDefault()} className="text-destructive">
              <Trash className="mr-2 h-4 w-4" />
              {t('instructors.actions.delete')}
            </DropdownMenuItem>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>{t('instructors.messages.deleteConfirmTitle')}</AlertDialogTitle>
              <AlertDialogDescription>
                {t('instructors.messages.deleteConfirmDescription', { 
                  name: `${instructor.name} ${instructor.surname}` 
                })}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>{t('common.actions.cancel')}</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                disabled={isDeleting}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {isDeleting ? t('instructors.messages.deleting') : t('instructors.actions.delete')}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

const SchoolsCell = ({ schools }: { schools: any[] }) => {
  const { t } = useSafeTranslation();
  
  return (
    <div className="text-sm">
      {t('instructors.messages.schoolCount', { count: schools.length })}
    </div>
  );
};

export const useInstructorColumns = (): ColumnDef<Instructor>[] => {
  const { t } = useSafeTranslation();

  return useMemo(() => [
    {
      accessorKey: "name",
      header: t('instructors.details.name'),
      cell: ({ row }) => (
        <div>
          <Link href={`/instructors/${row.original.id}`} className="hover:underline">
            <div className="font-medium">
              {row.original.name} {row.original.surname}
            </div>
          </Link>
          <div className="text-sm text-muted-foreground">
            {row.original.email}
          </div>
        </div>
      ),
    },
    {
      accessorKey: "phone",
      header: t('instructors.details.phone'),
    },
    {
      accessorKey: "branches",
      header: t('instructors.details.specializations'),
      cell: ({ row }) => (
        <div className="flex flex-wrap gap-1">
          {row.original.branches.map((branch) => (
            <Badge key={branch.id} variant="secondary">
              {t(`common.branches.${branch.name}`)}
            </Badge>
          ))}
        </div>
      ),
    },
    {
      accessorKey: "schools",
      header: t('instructors.details.schools'),
      cell: ({ row }) => <SchoolsCell schools={row.original.schools} />,
    },
    {
      id: "actions",
      header: t('common.actionsHeader'),
      cell: ({ row }) => <ActionsCell instructor={row.original} />,
    },
  ], [t]);
};