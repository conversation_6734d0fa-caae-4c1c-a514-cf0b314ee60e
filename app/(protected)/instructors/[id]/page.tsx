import { notFound } from "next/navigation";
import { getInstructorById, getSchools } from "@/lib/actions";
import InstructorDetailClient from "./instructor-detail-client";

interface InstructorDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function InstructorDetailPage({ params }: InstructorDetailPageProps) {
  try {
    const { id } = await params;
    const [instructor, schools] = await Promise.all([
      getInstructorById(id),
      getSchools()
    ]);

    if (!instructor) {
      notFound();
    }

    return <InstructorDetailClient instructor={instructor} schools={schools} />;
  } catch (error) {
    console.error("Error loading instructor data:", error);
    notFound();
  }
}
