import { notFound } from "next/navigation";
import { getInstructorById, getBranches, getSchools } from "@/lib/actions";
import EditInstructorClient from "./edit-instructor-client";

interface EditInstructorPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function EditInstructorPage({ params }: EditInstructorPageProps) {
  try {
    const { id } = await params;
    const [instructor, branches, schools] = await Promise.all([
      getInstructorById(id),
      getBranches(),
      getSchools()
    ]);

    if (!instructor) {
      notFound();
    }

    return (
      <EditInstructorClient 
        instructor={instructor} 
        branches={branches}
        schools={schools}
      />
    );
  } catch (error) {
    console.error("Error loading instructor data:", error);
    notFound();
  }
}
