"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ArrowLeft } from "lucide-react";
import { updateInstructor, updateInstructorBranches, updateInstructorSchools } from "@/lib/actions";
import { Branch } from "@/lib/types";
import { MultiSelect, Option } from "@/components/ui/multi-select";
import { DatePicker } from "@/components/ui/date-picker";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { formatDateToLocalString } from "@/lib/utils/date-formatter";
import { useToast } from "@/hooks/use-toast";
import Link from "next/link";

interface InstructorData {
  id: string;
  name: string;
  surname: string;
  email: string;
  phone: string;
  nationalId?: string | null;
  birthDate?: string | null;
  address?: string | null;
  salary?: string | null;
  branches: Array<{ id: string; name: string }>;
  schools: string[]; // Array of school IDs
}

interface EditInstructorClientProps {
  instructor: InstructorData;
  branches: Branch[];
  schools: Array<{ id: string; name: string; foundedYear: number }>;
}

export default function EditInstructorClient({ instructor, branches, schools }: EditInstructorClientProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const { t } = useSafeTranslation();
  const { toast } = useToast();

  const [formData, setFormData] = useState({
    name: instructor.name,
    surname: instructor.surname,
    email: instructor.email,
    phone: instructor.phone,
    nationalId: instructor.nationalId || '',
    address: instructor.address || '',
    salary: instructor.salary || '',
    birthDate: instructor.birthDate || '',
  });

  const [selectedBranches, setSelectedBranches] = useState<string[]>(
    instructor.branches.map(branch => branch.id).filter(Boolean) as string[]
  );

  const [selectedSchools, setSelectedSchools] = useState<string[]>(
    instructor.schools
  );


  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Prepare data with proper types for the action
      const updateData = {
        ...formData,
        salary: formData.salary ? parseFloat(formData.salary) : undefined
      };

      // Update basic instructor information
      const updateInstructorResult =  await updateInstructor(instructor.id, updateData);
      if(!updateInstructorResult.success){
        let errorDescriptionKey = '';
        if(updateInstructorResult.errorType == 'BusinessRuleError'){
          errorDescriptionKey = `errors.${updateInstructorResult.error}`;
        }else{
          errorDescriptionKey = 'instructors.messages.updateError';
        }
        toast({
          title: t('common.error'),
          description: t(errorDescriptionKey),
          variant: "destructive",
        });
        return;
      }

      // Update branch associations
      await updateInstructorBranches(instructor.id, selectedBranches);

      // Update school associations
      await updateInstructorSchools(instructor.id, selectedSchools);

      toast({
        title: t('instructors.messages.updateSuccess'),
        description: t('instructors.messages.instructorUpdated'),
      });

      router.push('/instructors');
      router.refresh();
    } catch (error) {
      console.error('Failed to update instructor:', error);
      toast({
        title: t('instructors.messages.updateError'),
        description: t('instructors.messages.updateFailed'),
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const branchOptions: Option[] = branches
    .filter(branch => branch.id && branch.name)
    .map(branch => ({
      value: branch.id!,
      label: t(`common.branches.${branch.name}`, { ns: 'shared' }),
    }));

  const schoolOptions: Option[] = schools.map(school => ({
    value: school.id,
    label: school.name,
  }));

  return (
    <div className="container mx-auto px-4 py-6 max-w-4xl">
      <div className="mb-6">
        <Link href="/instructors">
          <Button variant="ghost" size="sm" className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.actions.back')}
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">{t('instructors.actions.edit')}</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('instructors.form.editTitle')}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">{t('instructors.form.name')} *</Label>
                <Input
                  id="name"
                  name="name"
                  type="text"
                  required
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder={t('instructors.form.namePlaceholder')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="surname">{t('instructors.form.surname')} *</Label>
                <Input
                  id="surname"
                  name="surname"
                  type="text"
                  required
                  value={formData.surname}
                  onChange={handleInputChange}
                  placeholder={t('instructors.form.surnamePlaceholder')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">{t('instructors.form.email')} *</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  required
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder={t('instructors.form.emailPlaceholder')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">{t('instructors.form.phone')}</Label>
                <Input
                  id="phone"
                  name="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={handleInputChange}
                  placeholder={t('instructors.form.phonePlaceholder')}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="nationalId">{t('instructors.form.nationalId')}</Label>
                <Input
                  id="nationalId"
                  name="nationalId"
                  type="text"
                  value={formData.nationalId}
                  onChange={handleInputChange}
                  placeholder={t('instructors.form.nationalIdPlaceholder')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="birthDate">{t('instructors.form.birthDate')}</Label>
                <DatePicker
                  date={formData.birthDate ? new Date(formData.birthDate) : undefined}
                  onSelect={(date) => {
                    if (date) {
                      setFormData(prev => ({ ...prev, birthDate: formatDateToLocalString(date) }));
                    } else {
                      setFormData(prev => ({ ...prev, birthDate: '' }));
                    }
                  }}
                  placeholder={t('instructors.form.birthDatePlaceholder')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">{t('instructors.form.address')}</Label>
                <Input
                  id="address"
                  name="address"
                  type="text"
                  value={formData.address}
                  onChange={handleInputChange}
                  placeholder={t('instructors.form.addressPlaceholder')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="salary">{t('instructors.form.salary')}</Label>
                <Input
                  id="salary"
                  name="salary"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.salary}
                  onChange={handleInputChange}
                  placeholder={t('instructors.form.salaryPlaceholder')}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label>{t('instructors.form.branches')}</Label>
                <MultiSelect
                  options={branchOptions}
                  selected={selectedBranches}
                  onChange={setSelectedBranches}
                  placeholder={t('instructors.form.selectBranches')}
                />
              </div>

              <div className="space-y-2">
                <Label>{t('instructors.form.schools')}</Label>
                <MultiSelect
                  options={schoolOptions}
                  selected={selectedSchools}
                  onChange={setSelectedSchools}
                  placeholder={t('instructors.form.selectSchools')}
                />
              </div>
            </div>

            <div className="flex justify-end gap-4">
              <Link href="/instructors">
                <Button variant="outline" type="button">
                  {t('common.actions.cancel')}
                </Button>
              </Link>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? t('common.loading') : t('instructors.actions.update')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
