import { notFound } from "next/navigation";
import { getFacilityById } from "@/lib/actions/facilities";
import EditFacilityClient from "./edit-facility-client";

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function EditFacilityPage({ params }: PageProps) {
  try {
    const { id } = await params;
    const facility = await getFacilityById(id);
    
    if (!facility) {
      notFound();
    }

    return <EditFacilityClient facility={facility} />;
  } catch (error) {
    console.error('Error fetching facility:', error);
    notFound();
  }
}
