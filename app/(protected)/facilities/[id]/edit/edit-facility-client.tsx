"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ArrowLeft } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { updateFacility } from "@/lib/actions/facilities";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { Facility } from "@/lib/types";
import {useToast} from "@/hooks/use-toast";

interface EditFacilityClientProps {
  facility: Facility;
}

export default function EditFacilityClient({ facility: initialFacility }: EditFacilityClientProps) {
  const router = useRouter();
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [facility, setFacility] = useState({
    name: initialFacility.name || "",
    type: initialFacility.type || ("field" as "field" | "court" | "pool" | "studio" | "other"),
    address: initialFacility.address || "",
    capacity: {
      total: initialFacility.capacity?.total || initialFacility.totalCapacity || 0,
      currentlyOccupied: initialFacility.capacity?.currentlyOccupied || initialFacility.currentlyOccupied || 0,
      dimensions: {
        length: initialFacility.capacity?.dimensions?.length || (initialFacility.length ? parseFloat(initialFacility.length) : 0),
        width: initialFacility.capacity?.dimensions?.width || (initialFacility.width ? parseFloat(initialFacility.width) : 0),
        unit: (initialFacility.capacity?.dimensions?.unit || initialFacility.dimensionUnit || "meters") as "meters" | "feet",
      },
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      const result = await updateFacility(initialFacility.id, {
        name: facility.name,
        type: facility.type as 'field' | 'court' | 'pool' | 'studio' | 'other',
        address: facility.address,
        totalCapacity: facility.capacity.total,
        length: facility.capacity.dimensions.length.toString(),
        width: facility.capacity.dimensions.width.toString(),
        dimensionUnit: facility.capacity.dimensions.unit,
      });
      if(result.success){
        toast({
          title: t('common.success'),
          description: t('common.messages.updateSuccess'),
        });
        router.push("/facilities");
      }else{
        let errorDescriptionKey = '';
        if(result.errorType == 'BusinessRuleError'){
          errorDescriptionKey = `errors.${result.error}`;
        }else{
          errorDescriptionKey = 'common.messages.updateError';
        }
        toast({
          title: t('common.error'),
          description: t(errorDescriptionKey),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error updating facility:", error);
      toast({
        title: t('common.error'),
        description: t('common.messages.updateError'),
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-6">
      <Button
        variant="ghost"
        className="mb-6"
        onClick={() => router.push("/facilities")}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        {t('common.actions.back')}
      </Button>

      <Card>
        <CardHeader>
          <CardTitle>{t('facilities.edit')}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">{t('facilities.details.name')}</Label>
                <Input
                  id="name"
                  value={facility.name}
                  onChange={(e) =>
                    setFacility((prev) => ({ ...prev, name: e.target.value }))
                  }
                  placeholder={t('facilities.placeholders.enterName')}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">{t('facilities.details.type')}</Label>
                <Select
                  value={facility.type}
                  onValueChange={(value: "field" | "court" | "pool" | "studio" | "other") =>
                    setFacility((prev) => ({ ...prev, type: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder={t('facilities.placeholders.selectType')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="field">{t('facilities.types.field')}</SelectItem>
                    <SelectItem value="court">{t('facilities.types.court')}</SelectItem>
                    <SelectItem value="pool">{t('facilities.types.pool')}</SelectItem>
                    <SelectItem value="studio">{t('facilities.types.studio')}</SelectItem>
                    <SelectItem value="other">{t('facilities.types.other')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">{t('facilities.details.address')}</Label>
                <Input
                  id="address"
                  value={facility.address}
                  onChange={(e) =>
                    setFacility((prev) => ({ ...prev, address: e.target.value }))
                  }
                  placeholder={t('facilities.placeholders.enterAddress')}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="capacity">{t('facilities.details.maxCapacity')}</Label>
                <Input
                  id="capacity"
                  type="number"
                  min="1"
                  value={facility.capacity.total}
                  onChange={(e) =>
                    setFacility((prev) => ({
                      ...prev,
                      capacity: {
                        ...prev.capacity,
                        total: parseInt(e.target.value) || 0,
                      },
                    }))
                  }
                  placeholder={t('facilities.placeholders.enterCapacity')}
                />
              </div>

              <div className="space-y-2">
                <Label>{t('facilities.details.dimensions')}</Label>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="length">{t('facilities.details.length')}</Label>
                    <Input
                      id="length"
                      type="number"
                      step="0.1"
                      min="0"
                      value={facility.capacity.dimensions.length}
                      onChange={(e) =>
                        setFacility((prev) => ({
                          ...prev,
                          capacity: {
                            ...prev.capacity,
                            dimensions: {
                              ...prev.capacity.dimensions,
                              length: parseFloat(e.target.value) || 0,
                            },
                          },
                        }))
                      }
                      placeholder={t('facilities.placeholders.lengthIn', { unit: t(`facilities.units.${facility.capacity.dimensions.unit}`) })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="width">{t('facilities.details.width')}</Label>
                    <Input
                      id="width"
                      type="number"
                      step="0.1"
                      min="0"
                      value={facility.capacity.dimensions.width}
                      onChange={(e) =>
                        setFacility((prev) => ({
                          ...prev,
                          capacity: {
                            ...prev.capacity,
                            dimensions: {
                              ...prev.capacity.dimensions,
                              width: parseFloat(e.target.value) || 0,
                            },
                          },
                        }))
                      }
                      placeholder={t('facilities.placeholders.widthIn', { unit: t(`facilities.units.${facility.capacity.dimensions.unit}`) })}
                    />
                  </div>
                </div>
                <div className="mt-2">
                  <Label htmlFor="unit">{t('facilities.details.unit')}</Label>
                  <Select
                    value={facility.capacity.dimensions.unit}
                    onValueChange={(value: "meters" | "feet") =>
                      setFacility((prev) => ({
                        ...prev,
                        capacity: {
                          ...prev.capacity,
                          dimensions: {
                            ...prev.capacity.dimensions,
                            unit: value,
                          },
                        },
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t('facilities.placeholders.selectUnit')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="meters">{t('facilities.units.meters')}</SelectItem>
                      <SelectItem value="feet">{t('facilities.units.feet')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/facilities")}
                disabled={isSubmitting}
              >
                {t('common.actions.cancel')}
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? t('common.actions.saving') : t('common.actions.save')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
