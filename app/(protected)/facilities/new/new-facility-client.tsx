"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { MapPin, ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { createFacility } from "@/lib/actions/facilities";
import {useToast} from "@/hooks/use-toast";

export default function NewFacilityClient() {
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [unit, setUnit] = useState<"meters" | "feet">("meters");

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);

    try {
      const formData = new FormData(e.currentTarget);
      const facilityData = {
        name: formData.get("name") as string,
        address: formData.get("address") as string,
        type: formData.get("type") as 'field' | 'court' | 'pool' | 'studio' | 'other',
        totalCapacity: parseInt(formData.get("capacity") as string) || undefined,
        length: formData.get("length") ? (formData.get("length") as string) : undefined,
        width: formData.get("width") ? (formData.get("width") as string) : undefined,
        dimensionUnit: unit,
      };

      const result = await createFacility(facilityData);
      if(result.success){
        toast({
          title: t('common.success'),
          description: t('common.messages.createSuccess')
        });
        router.push("/facilities");
        router.refresh();
      }else{
        let errorDescriptionKey = '';
        if(result.errorType == 'BusinessRuleError'){
          errorDescriptionKey = `errors.${result.error}`;
        }else{
          errorDescriptionKey = 'common.messages.createError';
        }
        toast({
          title: t('common.error'),
          description: t(errorDescriptionKey),
          variant: "destructive",
        });
      }

    } catch (error) {
      console.error("Failed to create facility:", error);
      toast({
        title: t('common.error'),
        description: t('common.messages.createError'),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-6 max-w-4xl">
      <div className="mb-6">
        <Link href="/facilities">
          <Button variant="ghost" size="sm" className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.actions.back')}
          </Button>
        </Link>
        <div className="flex items-center gap-2">
          <MapPin className="h-6 w-6 text-primary" />
          <h1 className="text-3xl font-bold">{t('facilities.actions.add')}</h1>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('facilities.form.title')}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">{t('facilities.form.name')} *</Label>
                <Input
                  id="name"
                  name="name"
                  type="text"
                  required
                  placeholder={t('facilities.form.namePlaceholder')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">{t('facilities.form.type')} *</Label>
                <Select name="type" required>
                  <SelectTrigger>
                    <SelectValue placeholder={t('facilities.form.selectType')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="field">{t('facilities.types.field')}</SelectItem>
                    <SelectItem value="court">{t('facilities.types.court')}</SelectItem>
                    <SelectItem value="pool">{t('facilities.types.pool')}</SelectItem>
                    <SelectItem value="studio">{t('facilities.types.studio')}</SelectItem>
                    <SelectItem value="other">{t('facilities.types.other')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="address">{t('facilities.form.address')}</Label>
                <Input
                  id="address"
                  name="address"
                  type="text"
                  placeholder={t('facilities.form.addressPlaceholder')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="capacity">{t('facilities.form.capacity')}</Label>
                <Input
                  id="capacity"
                  name="capacity"
                  type="number"
                  min="1"
                  placeholder={t('facilities.form.capacityPlaceholder')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="unit">{t('facilities.form.dimensionUnit')}</Label>
                <Select value={unit} onValueChange={(value: "meters" | "feet") => setUnit(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="meters">{t('facilities.form.meters')}</SelectItem>
                    <SelectItem value="feet">{t('facilities.form.feet')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="length">{t('facilities.form.length')}</Label>
                <Input
                  id="length"
                  name="length"
                  type="text"
                  placeholder={unit === "meters" ? t('facilities.form.lengthPlaceholderMeters') : t('facilities.form.lengthPlaceholderFeet')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="width">{t('facilities.form.width')}</Label>
                <Input
                  id="width"
                  name="width"
                  type="text"
                  placeholder={unit === "meters" ? t('facilities.form.widthPlaceholderMeters') : t('facilities.form.widthPlaceholderFeet')}
                />
              </div>
            </div>

            <div className="flex justify-end gap-4">
              <Link href="/facilities">
                <Button variant="outline" type="button">
                  {t('common.actions.cancel')}
                </Button>
              </Link>
              <Button type="submit" disabled={loading}>
                {loading ? t('common.loading') : t('facilities.actions.create')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
