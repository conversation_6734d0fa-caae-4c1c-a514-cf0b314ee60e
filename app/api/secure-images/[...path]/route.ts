import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth-config';
import { readFile, access } from 'fs/promises';
import { join, resolve, normalize } from 'path';
import { constants } from 'fs';
import { getToken } from 'next-auth/jwt';
import { extractTenantId } from '@/lib/middleware-tenant-utils';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    // 0. AWAIT PARAMS (Next.js 15 requirement)
    const resolvedParams = await params;

    // 1. AUTHENTICATION CHECK - Must be authenticated
    const session = await getServerSession(authOptions);

    if (!session) {
      console.warn('Unauthorized image access attempt - no session');
      return new NextResponse('Unauthorized - No session', { status: 401 });
    }

    // Get tenant ID using the same method as middleware
    let userTenantId: string | null = null;

    // Try to get from session first
    if (session.tenantId) {
      userTenantId = session.tenantId;
    } else {
      // Fallback: extract from JWT token like middleware does
      const token = await getToken({
        req: request,
        secret: process.env.NEXTAUTH_SECRET,
        secureCookie: process.env.NODE_ENV === 'production'
      });

      if (token) {
        userTenantId = extractTenantId(token);
      }
    }

    console.log('Secure image request - Auth info:', {
      hasSession: !!session,
      sessionTenantId: session?.tenantId,
      extractedTenantId: userTenantId,
      hasToken: !!await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET })
    });

    if (!userTenantId) {
      console.warn('Unauthorized image access attempt - no tenant ID found');
      return new NextResponse('Unauthorized - No tenant ID', { status: 401 });
    }

    // userTenantId is already set above
    const pathSegments = resolvedParams.path;

    // 2. PATH VALIDATION - Must have exactly 2 segments: [tenantId, filename]
    if (!pathSegments || pathSegments.length !== 2) {
      console.warn('Invalid image path structure:', pathSegments);
      return new NextResponse('Invalid path structure', { status: 400 });
    }

    const [requestedTenantId, filename] = pathSegments;

    // 3. TENANT ISOLATION - User can only access their own tenant's images
    console.log('🔒 Tenant isolation check:', {
      userTenantId,
      requestedTenantId,
      pathSegments,
      fullPath: resolvedParams.path,
      match: requestedTenantId === userTenantId
    });

    if (requestedTenantId !== userTenantId) {
      console.warn(`🚨 TENANT ISOLATION VIOLATION: User ${userTenantId} tried to access ${requestedTenantId}`);
      return new NextResponse('Forbidden - Tenant access violation', { status: 403 });
    }

    // 4. FILENAME SECURITY - Prevent path traversal attacks
    if (!filename || 
        filename.includes('..') || 
        filename.includes('/') || 
        filename.includes('\\') ||
        filename.includes('\0') ||
        filename.startsWith('.') ||
        filename.length > 255) {
      console.warn('Malicious filename detected:', filename);
      return new NextResponse('Invalid filename', { status: 400 });
    }

    // 5. FILE EXTENSION VALIDATION - Only allow image files
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];
    const fileExtension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    if (!allowedExtensions.includes(fileExtension)) {
      console.warn('Invalid file extension:', fileExtension);
      return new NextResponse('Invalid file type', { status: 400 });
    }

    // 6. CONSTRUCT SECURE FILE PATH
    const uploadsDir = join(process.cwd(), 'private', 'uploads', requestedTenantId);
    const requestedFilePath = join(uploadsDir, filename);
    
    // 7. PATH TRAVERSAL PROTECTION - Ensure resolved path is within uploads directory
    const resolvedPath = resolve(requestedFilePath);
    const resolvedUploadsDir = resolve(uploadsDir);
    
    if (!resolvedPath.startsWith(resolvedUploadsDir + '/') && resolvedPath !== resolvedUploadsDir) {
      console.warn('Path traversal attempt detected:', resolvedPath);
      return new NextResponse('Path traversal detected', { status: 400 });
    }

    // 8. FILE EXISTENCE CHECK
    try {
      await access(requestedFilePath, constants.F_OK);
    } catch {
      console.warn('File not found:', requestedFilePath);
      return new NextResponse('File not found', { status: 404 });
    }

    // 9. READ AND SERVE FILE
    const fileBuffer = await readFile(requestedFilePath);
    
    // 10. DETERMINE CONTENT TYPE
    let contentType = 'application/octet-stream';
    switch (fileExtension) {
      case '.jpg':
      case '.jpeg':
        contentType = 'image/jpeg';
        break;
      case '.png':
        contentType = 'image/png';
        break;
      case '.webp':
        contentType = 'image/webp';
        break;
      case '.gif':
        contentType = 'image/gif';
        break;
    }

    // 11. SECURITY HEADERS AND RESPONSE
    return new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'private, max-age=3600', // Private cache for 1 hour
        'Content-Disposition': 'inline',
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'Content-Security-Policy': "default-src 'none'",
      },
    });

  } catch (error) {
    console.error('Secure image serving error:', error);
    return new NextResponse('Internal server error', { status: 500 });
  }
}
